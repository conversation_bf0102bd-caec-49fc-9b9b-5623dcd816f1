{"__meta": {"id": "01K2FP8G7RF5WY353NX7GG62MA", "datetime": "2025-08-12 17:46:44", "utime": **********.345352, "method": "POST", "uri": "/admin/ecommerce/options/create", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1755020803.34489, "end": **********.345368, "duration": 1.0004777908325195, "duration_str": "1s", "measures": [{"label": "Booting", "start": 1755020803.34489, "relative_start": 0, "end": **********.147743, "relative_end": **********.147743, "duration": 0.****************, "duration_str": "803ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.147762, "relative_start": 0.****************, "end": **********.34537, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "198ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.204076, "relative_start": 0.****************, "end": **********.21176, "relative_end": **********.21176, "duration": 0.007683992385864258, "duration_str": "7.68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.341194, "relative_start": 0.****************, "end": **********.34289, "relative_end": **********.34289, "duration": 0.0016961097717285156, "duration_str": "1.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "lcoal", "Debug Mode": "Enabled", "URL": "tesmods.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 13, "nb_statements": 13, "nb_visible_statements": 13, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.039290000000000005, "accumulated_duration_str": "39.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.2250068, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 0, "width_percent": 1.044}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 105}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.2314298, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 1.044, "width_percent": 1.018}, {"sql": "insert into `ec_global_options` (`name`, `option_type`, `required`, `updated_at`, `created_at`) values ('Paint Codes', '<PERSON>haqi\\\\Ecommerce\\\\Option\\\\OptionType\\\\Checkbox', '0', '2025-08-12 17:46:44', '2025-08-12 17:46:44')", "type": "query", "params": [], "bindings": ["Paint Codes", "Shaqi\\Ecommerce\\Option\\OptionType\\Checkbox", "0", "2025-08-12 17:46:44", "2025-08-12 17:46:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductOptionController.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductOptionController.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.2563221, "duration": 0.00414, "duration_str": "4.14ms", "memory": 0, "memory_str": null, "filename": "ProductOptionController.php:46", "source": {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductOptionController.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductOptionController.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FProductOptionController.php&line=46", "ajax": false, "filename": "ProductOptionController.php", "line": "46"}, "connection": "tesmods", "explain": null, "start_percent": 2.062, "width_percent": 10.537}, {"sql": "delete from `ec_global_option_value` where `ec_global_option_value`.`option_id` = 7 and `ec_global_option_value`.`option_id` is not null and `id` not in (null, null, null, null, null, null, null) order by `order` asc", "type": "query", "params": [], "bindings": [7, null, null, null, null, null, null, null], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductOptionController.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductOptionController.php", "line": 50}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.273742, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "ProductOptionController.php:50", "source": {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductOptionController.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductOptionController.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FProductOptionController.php&line=50", "ajax": false, "filename": "ProductOptionController.php", "line": "50"}, "connection": "tesmods", "explain": null, "start_percent": 12.599, "width_percent": 3.181}, {"sql": "insert into `ec_global_option_value` (`option_value`, `affect_price`, `affect_type`, `order`, `option_id`, `updated_at`, `created_at`) values ('PPSB – Deep Blue Metallic', 0, '0', 0, 7, '2025-08-12 17:46:44', '2025-08-12 17:46:44')", "type": "query", "params": [], "bindings": ["PPSB – Deep Blue Metallic", 0, "0", 0, 7, "2025-08-12 17:46:44", "2025-08-12 17:46:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductOptionController.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductOptionController.php", "line": 51}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.276998, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "ProductOptionController.php:51", "source": {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductOptionController.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductOptionController.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FProductOptionController.php&line=51", "ajax": false, "filename": "ProductOptionController.php", "line": "51"}, "connection": "tesmods", "explain": null, "start_percent": 15.78, "width_percent": 10.181}, {"sql": "insert into `ec_global_option_value` (`option_value`, `affect_price`, `affect_type`, `order`, `option_id`, `updated_at`, `created_at`) values ('PPSW – Pearl White Multi-Coat', 0, '0', 1, 7, '2025-08-12 17:46:44', '2025-08-12 17:46:44')", "type": "query", "params": [], "bindings": ["PPSW – Pearl White Multi-Coat", 0, "0", 1, 7, "2025-08-12 17:46:44", "2025-08-12 17:46:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductOptionController.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductOptionController.php", "line": 51}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.2841349, "duration": 0.00424, "duration_str": "4.24ms", "memory": 0, "memory_str": null, "filename": "ProductOptionController.php:51", "source": {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductOptionController.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductOptionController.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FProductOptionController.php&line=51", "ajax": false, "filename": "ProductOptionController.php", "line": "51"}, "connection": "tesmods", "explain": null, "start_percent": 25.961, "width_percent": 10.792}, {"sql": "insert into `ec_global_option_value` (`option_value`, `affect_price`, `affect_type`, `order`, `option_id`, `updated_at`, `created_at`) values ('PN00 – Quicksilver', 0, '0', 2, 7, '2025-08-12 17:46:44', '2025-08-12 17:46:44')", "type": "query", "params": [], "bindings": ["PN00 – Quicksilver", 0, "0", 2, 7, "2025-08-12 17:46:44", "2025-08-12 17:46:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductOptionController.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductOptionController.php", "line": 51}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.290204, "duration": 0.00431, "duration_str": "4.31ms", "memory": 0, "memory_str": null, "filename": "ProductOptionController.php:51", "source": {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductOptionController.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductOptionController.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FProductOptionController.php&line=51", "ajax": false, "filename": "ProductOptionController.php", "line": "51"}, "connection": "tesmods", "explain": null, "start_percent": 36.752, "width_percent": 10.97}, {"sql": "insert into `ec_global_option_value` (`option_value`, `affect_price`, `affect_type`, `order`, `option_id`, `updated_at`, `created_at`) values ('PBSB – Solid Black', 0, '0', 3, 7, '2025-08-12 17:46:44', '2025-08-12 17:46:44')", "type": "query", "params": [], "bindings": ["PBSB – Solid Black", 0, "0", 3, 7, "2025-08-12 17:46:44", "2025-08-12 17:46:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductOptionController.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductOptionController.php", "line": 51}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.296321, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "ProductOptionController.php:51", "source": {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductOptionController.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductOptionController.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FProductOptionController.php&line=51", "ajax": false, "filename": "ProductOptionController.php", "line": "51"}, "connection": "tesmods", "explain": null, "start_percent": 47.722, "width_percent": 9.773}, {"sql": "insert into `ec_global_option_value` (`option_value`, `affect_price`, `affect_type`, `order`, `option_id`, `updated_at`, `created_at`) values ('PN01 – <PERSON>ealth Gray', 0, '0', 4, 7, '2025-08-12 17:46:44', '2025-08-12 17:46:44')", "type": "query", "params": [], "bindings": ["PN01 – <PERSON><PERSON>th Gray", 0, "0", 4, 7, "2025-08-12 17:46:44", "2025-08-12 17:46:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductOptionController.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductOptionController.php", "line": 51}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.3028421, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "ProductOptionController.php:51", "source": {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductOptionController.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductOptionController.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FProductOptionController.php&line=51", "ajax": false, "filename": "ProductOptionController.php", "line": "51"}, "connection": "tesmods", "explain": null, "start_percent": 57.496, "width_percent": 10.155}, {"sql": "insert into `ec_global_option_value` (`option_value`, `affect_price`, `affect_type`, `order`, `option_id`, `updated_at`, `created_at`) values ('PR01 – Ultra Red', 0, '0', 5, 7, '2025-08-12 17:46:44', '2025-08-12 17:46:44')", "type": "query", "params": [], "bindings": ["PR01 – Ultra Red", 0, "0", 5, 7, "2025-08-12 17:46:44", "2025-08-12 17:46:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductOptionController.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductOptionController.php", "line": 51}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.308652, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "ProductOptionController.php:51", "source": {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductOptionController.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductOptionController.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FProductOptionController.php&line=51", "ajax": false, "filename": "ProductOptionController.php", "line": "51"}, "connection": "tesmods", "explain": null, "start_percent": 67.651, "width_percent": 9.85}, {"sql": "insert into `ec_global_option_value` (`option_value`, `affect_price`, `affect_type`, `order`, `option_id`, `updated_at`, `created_at`) values ('Other', 0, '0', 6, 7, '2025-08-12 17:46:44', '2025-08-12 17:46:44')", "type": "query", "params": [], "bindings": ["Other", 0, "0", 6, 7, "2025-08-12 17:46:44", "2025-08-12 17:46:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductOptionController.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductOptionController.php", "line": 51}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.314513, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "ProductOptionController.php:51", "source": {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductOptionController.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductOptionController.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FProductOptionController.php&line=51", "ajax": false, "filename": "ProductOptionController.php", "line": "51"}, "connection": "tesmods", "explain": null, "start_percent": 77.501, "width_percent": 10.155}, {"sql": "select `lang_code`, `lang_is_default` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 20}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3259668, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 87.656, "width_percent": 1.094}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `reference_user`, `reference_id`, `reference_name`, `type`, `created_at`, `updated_at`, `request`) values ('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '127.0.0.1', 'global-option', 'created', 1, 1, 7, 'Paint Codes', 'info', '2025-08-12 17:46:44', '2025-08-12 17:46:44', '{\\\"name\\\":\\\"Paint Codes\\\",\\\"options\\\":[{\\\"option_value\\\":\\\"PPSB \\\\u2013 Deep Blue Metallic\\\",\\\"affect_price\\\":null,\\\"affect_type\\\":\\\"0\\\"},{\\\"option_value\\\":\\\"PPSW \\\\u2013 Pearl White Multi-Coat\\\",\\\"affect_price\\\":null,\\\"affect_type\\\":\\\"0\\\"},{\\\"option_value\\\":\\\"PN00 \\\\u2013 Quicksilver\\\",\\\"affect_price\\\":null,\\\"affect_type\\\":\\\"0\\\"},{\\\"option_value\\\":\\\"PBSB \\\\u2013 Solid Black\\\",\\\"affect_price\\\":null,\\\"affect_type\\\":\\\"0\\\"},{\\\"option_value\\\":\\\"PN01 \\\\u2013 Stealth Gray\\\",\\\"affect_price\\\":null,\\\"affect_type\\\":\\\"0\\\"},{\\\"option_value\\\":\\\"PR01 \\\\u2013 Ultra Red\\\",\\\"affect_price\\\":null,\\\"affect_type\\\":\\\"0\\\"},{\\\"option_value\\\":\\\"Other\\\",\\\"affect_price\\\":null,\\\"affect_type\\\":\\\"0\\\"}],\\\"submitter\\\":\\\"save\\\",\\\"option_type\\\":\\\"Shaqi\\\\\\\\Ecommerce\\\\\\\\Option\\\\\\\\OptionType\\\\\\\\Checkbox\\\",\\\"required\\\":\\\"0\\\"}')", "type": "query", "params": [], "bindings": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "127.0.0.1", "global-option", "created", 1, 1, 7, "Paint Codes", "info", "2025-08-12 17:46:44", "2025-08-12 17:46:44", "{\"name\":\"Paint Codes\",\"options\":[{\"option_value\":\"PPSB \\u2013 Deep Blue Metallic\",\"affect_price\":null,\"affect_type\":\"0\"},{\"option_value\":\"PPSW \\u2013 Pearl White Multi-Coat\",\"affect_price\":null,\"affect_type\":\"0\"},{\"option_value\":\"PN00 \\u2013 Quicksilver\",\"affect_price\":null,\"affect_type\":\"0\"},{\"option_value\":\"PBSB \\u2013 Solid Black\",\"affect_price\":null,\"affect_type\":\"0\"},{\"option_value\":\"PN01 \\u2013 Stealth Gray\",\"affect_price\":null,\"affect_type\":\"0\"},{\"option_value\":\"PR01 \\u2013 Ultra Red\",\"affect_price\":null,\"affect_type\":\"0\"},{\"option_value\":\"Other\",\"affect_price\":null,\"affect_type\":\"0\"}],\"submitter\":\"save\",\"option_type\":\"Shaqi\\\\Ecommerce\\\\Option\\\\OptionType\\\\Checkbox\",\"required\":\"0\"}"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.331649, "duration": 0.00442, "duration_str": "4.42ms", "memory": 0, "memory_str": null, "filename": "AuditHandlerListener.php:60", "source": {"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FListeners%2FAuditHandlerListener.php&line=60", "ajax": false, "filename": "AuditHandlerListener.php", "line": "60"}, "connection": "tesmods", "explain": null, "start_percent": 88.75, "width_percent": 11.25}]}, "models": {"data": {"Shaqi\\Language\\Models\\Language": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Shaqi\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "https://tesmods.gc/admin/ecommerce/options/create", "action_name": "global-option.create.store", "controller_action": "Shaqi\\Ecommerce\\Http\\Controllers\\ProductOptionController@store", "uri": "POST admin/ecommerce/options/create", "controller": "S<PERSON><PERSON>\\Ecommerce\\Http\\Controllers\\ProductOptionController@store<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FProductOptionController.php&line=41\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Shaqi\\Ecommerce\\Http\\Controllers", "prefix": "admin/ecommerce/options", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FProductOptionController.php&line=41\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/ecommerce/src/Http/Controllers/ProductOptionController.php:41-60</a>", "middleware": "web, core, auth", "duration": "999ms", "peak_memory": "50MB", "response": "Redirect to https://tesmods.gc/admin/ecommerce/options", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-875579933 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-875579933\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-84784154 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">21kNLtyrrz1EnHCRTflWSzE24GnCtMPJmB7j18C5</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Paint Codes</span>\"\n  \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>option_value</span>\" => \"<span class=sf-dump-str title=\"25 characters\">PPSB &#8211; Deep Blue Metallic</span>\"\n      \"<span class=sf-dump-key>affect_price</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>affect_type</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>option_value</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PPSW &#8211; Pearl White Multi-Coat</span>\"\n      \"<span class=sf-dump-key>affect_price</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>affect_type</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>option_value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">PN00 &#8211; Quicksilver</span>\"\n      \"<span class=sf-dump-key>affect_price</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>affect_type</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>option_value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">PBSB &#8211; Solid Black</span>\"\n      \"<span class=sf-dump-key>affect_price</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>affect_type</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>option_value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">PN01 &#8211; Stealth Gray</span>\"\n      \"<span class=sf-dump-key>affect_price</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>affect_type</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>option_value</span>\" => \"<span class=sf-dump-str title=\"16 characters\">PR01 &#8211; Ultra Red</span>\"\n      \"<span class=sf-dump-key>affect_price</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>affect_type</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n    <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>option_value</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Other</span>\"\n      \"<span class=sf-dump-key>affect_price</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>affect_type</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>submitter</span>\" => \"<span class=sf-dump-str title=\"4 characters\">save</span>\"\n  \"<span class=sf-dump-key>option_type</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Shaqi\\Ecommerce\\Option\\OptionType\\Checkbox</span>\"\n  \"<span class=sf-dump-key>required</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-84784154\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1144480086 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">tesmods.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1060</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">https://tesmods.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">https://tesmods.gc/admin/ecommerce/options/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2560 characters\">shaqi_footprints_cookie=eyJpdiI6ImxHZGRqajhxQkFDcUMxb2VsdW16d1E9PSIsInZhbHVlIjoiajFBNUdGa2xjRmJPdHdMWFpTRiswdk14REJmbFh3TGp4V0JsUjZpSEhsSENiRWhSRlgwMEl3MTByNEs4UG5qanlaUEc2UlpiOFZVY0tad0g4NnRxc25hZFMvREs4K3U2NEkwcFZBQW9NK3FSdXBpZHpCYnkvUVVRU0xWMEEzMzUiLCJtYWMiOiI2OTk3Mzk3NTRlMzM2ZTdiMDExZWJmODJhZGExOTY4MmZhNmMzNjM3MDM1MWRmMTllMjY5NTUwYjBiODZjZjg1IiwidGFnIjoiIn0%3D; shaqi_footprints_cookie_data=eyJpdiI6ImF4TmlBNFJ3RTVxNFBhWUFLZkNIQWc9PSIsInZhbHVlIjoiVmxhcXR5bU4wOXU5dlhCTjVpK0JWZ2xrT3pZYXkwSllnQ3V6QklLcDRYZVpBeFhmWmJ3WGJwRTA1ZlZmcGkydGdRSHB3ZGJwdUJ3Uk9TYWYvcWtmalNoWi9mWW5SN2hzZTMvanN5cTB5TzBCaGs5NkhWVjljTFRzWWozK0pxN0p4Wjk1ckREWVhleTVjQmJZQ2ttOTlwQ1RDbHhtN3ZOMldQSUhrMExhOHFIc2VCRE9mLy83UmhCSEdkc00rckR1Y3N4NkxML1FNeGZyWitHUldZUkR0eHYyZTdBbEtMRUxqbUtGWithaGJjcVBIV2ZnaElSYllTbE96aTQ5MWNMSkNBa2wwOHM3RFQ0RW83aUpxZmVtYjU4aDhjbmo1clFSWTRHa2VzbEJLOWE3d1NoeVIvQnhrYUM1ejZUUy9qTkY0UDdsb1Q5Z3dybjVuLzZIaXRET1NadjJNSTJsbzNYd2U2aTFMOTMxYTJQejIzTU1IRFFpVm5Nd0xLdUk5Yjh2YXE1S01sRUQrMDB6OXI4U2pZeS9zQTAyaVI5TTN6bWVyV25vWGVtRWlHWkhLb1crRnVXVDJRbjJkNUY0OXM1MUUxMVpiRE9jd0wyQkg0Z0FpcDlzakRlT1NNWnVvSTI4MVNZaC9GZmhuTGNLWXBZRnNTNG9nRnY5Q3RxeTBxWUVRVjNoMDUvTkV1ajRpN096ZW4wOHlBPT0iLCJtYWMiOiJkYmI3NTYwZDUxZWJjY2I0ZmUxMmQ2YmU2YzUxYmQ3MjYzMzJlZmQ1MjMyZTBhNGNlOTUyMTgxMDhjMzhlNjU1IiwidGFnIjoiIn0%3D; cookie_for_consent=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlVHb3RYN0M5L2FRa3ovaExFNWVXQ3c9PSIsInZhbHVlIjoiMFU4V0hvaGRDVXI5K3RzdzdkMjZ1eDZ3cnhXQy9nRUpxekg1bFIxa0VoUTBTY2x6TTB4SlBGL1lkbVpOV2IyRitMcGsyMHFOS2ZMalRDQWZzM1g0MThCcWpheHNMSE5QT2g4ZUhhNFRxTnNUL0ZuKzhyNnpUbXI1V3JMSEhlT0laT2hXN0JUWWd4MEhXMXBhTzhaZkh2VkR4TXF4NXlDNnpUbzcxMjdnL1RwUzduZ05BMy9iTnZDNTlEdlBWaW5RNDkraERQRXI3RmgzNzEzclFrTkQ5S21kRmNWK1I0OVhGUGx2cXpzK0tJdz0iLCJtYWMiOiJhZDg0MDlkNzAxNWI5MjBiN2NlZmE5ZDRkNDExNDg3MjdiMTY1MWY0NDdjNjZkNmI5ZDA1ZTE0MWIwNzc4M2E1IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlgwQ2U4eG82aTJ3NjhnbzF0aEpXOGc9PSIsInZhbHVlIjoibkFsYjdHd0VqZWUyV0h6QzR4Q2VYekpwaGplVDZLT29OSStqOWZmQkxiVzNCQ2h0aEdRalNLblpsV0pmNFNSMWtMQjhTVm8raEswSk1BTjZLaFlDbElmSmlBS1pEVElGUGgrQ25wcGJGS2Z5RHpkdHBXNzY4aDJ6QTVzWWljNzgiLCJtYWMiOiJkNTY5YzM0ZTEyNDFmMDc4MzQ3ZGMzM2RiYjI2MTIxOGNiN2EwOWVmOTAxZDVjMDRhYWJhNzU4MGY4ZDY3ZGIyIiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6IllWdXhsVWFqdTFGZDg4OE4ycGt5bEE9PSIsInZhbHVlIjoiRE45QzV1QkFuQTNVcDdGUDVGQlA5bFBrNklCUGZvUDVaM1FHTERaa0tXZ0ViZTI3VUpxVGphbkE1czVaajc0VWlkOWVSbTBINTh6d0hmeUxubU1Vai9uSC9BMzJ4c3NCWUZXalRQaEErbkxZUTJjNFRVNmlHWTdHMlNwaVA5Z2giLCJtYWMiOiI0OWI0N2VmNmU2ZDY1MTA4ZGYyMGJjOGQxNmNmZTUyNTk1ZWZiNDVjY2NkYzRmZDY0MWM0ZmRiNzAzY2M4MGU4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1144480086\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1830067526 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>shaqi_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7a4e1a8bf2884ebcac0020a875b7168cc919bd88</span>\"\n  \"<span class=sf-dump-key>shaqi_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"346 characters\">{&quot;footprint&quot;:&quot;7a4e1a8bf2884ebcac0020a875b7168cc919bd88&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;tesmods.gc&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|IhQd5rv2OiHN31FogUE4N7nywrkbjngoKJ2fGSpGDJTc8ESMBnaVyTbMe1uM|$2y$12$Y0fKdUtNl.7uriE2jyqMteM5YHmP0ovxpe3RbMWYUS/IwySBzdTYK</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">21kNLtyrrz1EnHCRTflWSzE24GnCtMPJmB7j18C5</span>\"\n  \"<span class=sf-dump-key>shaqi_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">259NiGAyxSp79a8W1RViospfuj3oZ9d3ieXItl0L</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1830067526\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1414872627 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 17:46:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">https://tesmods.gc/admin/ecommerce/options</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1414872627\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1333032914 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">21kNLtyrrz1EnHCRTflWSzE24GnCtMPJmB7j18C5</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">https://tesmods.gc/admin/ecommerce/options/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">success_msg</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>success_msg</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Created successfully</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1333032914\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://tesmods.gc/admin/ecommerce/options/create", "action_name": "global-option.create.store", "controller_action": "Shaqi\\Ecommerce\\Http\\Controllers\\ProductOptionController@store"}, "badge": "302 Found"}}