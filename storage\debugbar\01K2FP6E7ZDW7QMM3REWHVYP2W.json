{"__meta": {"id": "01K2FP6E7ZDW7QMM3REWHVYP2W", "datetime": "2025-08-12 17:45:36", "utime": **********.768323, "method": "GET", "uri": "/admin/settings/license/verify?verified=true", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.54584, "end": **********.768342, "duration": 1.2225019931793213, "duration_str": "1.22s", "measures": [{"label": "Booting", "start": **********.54584, "relative_start": 0, "end": **********.693578, "relative_end": **********.693578, "duration": 1.***************, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.693595, "relative_start": 1.****************, "end": **********.768344, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "74.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.717338, "relative_start": 1.****************, "end": **********.727948, "relative_end": **********.727948, "duration": 0.010609865188598633, "duration_str": "10.61ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.762286, "relative_start": 1.****************, "end": **********.764554, "relative_end": **********.764554, "duration": 0.002268075942993164, "duration_str": "2.27ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "lcoal", "Debug Mode": "Enabled", "URL": "tesmods.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00074, "accumulated_duration_str": "740μs", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.746974, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 0, "width_percent": 52.703}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 105}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.755207, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 52.703, "width_percent": 47.297}]}, "models": {"data": {"Shaqi\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Shaqi\\Language\\Models\\Language": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://tesmods.gc/admin/settings/license/verify?verified=true", "action_name": "settings.license.verify.index", "controller_action": "\\Shaqi\\ShaqiActivator\\Http\\Controllers\\ShaqiActivatorController@getVerifyLicense", "uri": "GET admin/settings/license/verify", "controller": "\\Shaqi\\ShaqiActivator\\Http\\Controllers\\ShaqiActivatorController@getVerifyLicense<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Fmagic%2Fsrc%2FHttp%2FControllers%2FShaqiActivatorController.php&line=36\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Shaqi\\Setting\\Http\\Controllers", "prefix": "admin/settings/license", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Fmagic%2Fsrc%2FHttp%2FControllers%2FShaqiActivatorController.php&line=36\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/magic/src/Http/Controllers/ShaqiActivatorController.php:36-45</a>", "middleware": "web, core, auth", "duration": "1.22s", "peak_memory": "48MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>verified</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-801946407 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-801946407\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-657939601 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik80dS9XaXFCWGMzZmZlbGFXVDByQXc9PSIsInZhbHVlIjoiRlpCNTArQ3RTTGw3aEhkVGNQajBiQlo3SjJLTWVEOVViRFFkbVZabmkyKzNYMjNxV2Y2aVNDTXpWK3liWGh5Q3JxUmZtTW8yR0hDRzBKbTdBd29FYisrbUI0UGo2c0c4TWRLaTVJL1l3WFNRQldPVnZ5bEp2QzBCVTB5VnZRZ3oiLCJtYWMiOiJlMmRjYTFhOTc3MTcxNDI2MWY2MWRlMmNjNTg4MzdhZWUxMTlhY2NlMzMxNDliZGVmNmRhNmYwOGJlYjA4ZjU4IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">tesmods.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">https://tesmods.gc/admin/ecommerce/options/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2560 characters\">shaqi_footprints_cookie=eyJpdiI6ImxHZGRqajhxQkFDcUMxb2VsdW16d1E9PSIsInZhbHVlIjoiajFBNUdGa2xjRmJPdHdMWFpTRiswdk14REJmbFh3TGp4V0JsUjZpSEhsSENiRWhSRlgwMEl3MTByNEs4UG5qanlaUEc2UlpiOFZVY0tad0g4NnRxc25hZFMvREs4K3U2NEkwcFZBQW9NK3FSdXBpZHpCYnkvUVVRU0xWMEEzMzUiLCJtYWMiOiI2OTk3Mzk3NTRlMzM2ZTdiMDExZWJmODJhZGExOTY4MmZhNmMzNjM3MDM1MWRmMTllMjY5NTUwYjBiODZjZjg1IiwidGFnIjoiIn0%3D; shaqi_footprints_cookie_data=eyJpdiI6ImF4TmlBNFJ3RTVxNFBhWUFLZkNIQWc9PSIsInZhbHVlIjoiVmxhcXR5bU4wOXU5dlhCTjVpK0JWZ2xrT3pZYXkwSllnQ3V6QklLcDRYZVpBeFhmWmJ3WGJwRTA1ZlZmcGkydGdRSHB3ZGJwdUJ3Uk9TYWYvcWtmalNoWi9mWW5SN2hzZTMvanN5cTB5TzBCaGs5NkhWVjljTFRzWWozK0pxN0p4Wjk1ckREWVhleTVjQmJZQ2ttOTlwQ1RDbHhtN3ZOMldQSUhrMExhOHFIc2VCRE9mLy83UmhCSEdkc00rckR1Y3N4NkxML1FNeGZyWitHUldZUkR0eHYyZTdBbEtMRUxqbUtGWithaGJjcVBIV2ZnaElSYllTbE96aTQ5MWNMSkNBa2wwOHM3RFQ0RW83aUpxZmVtYjU4aDhjbmo1clFSWTRHa2VzbEJLOWE3d1NoeVIvQnhrYUM1ejZUUy9qTkY0UDdsb1Q5Z3dybjVuLzZIaXRET1NadjJNSTJsbzNYd2U2aTFMOTMxYTJQejIzTU1IRFFpVm5Nd0xLdUk5Yjh2YXE1S01sRUQrMDB6OXI4U2pZeS9zQTAyaVI5TTN6bWVyV25vWGVtRWlHWkhLb1crRnVXVDJRbjJkNUY0OXM1MUUxMVpiRE9jd0wyQkg0Z0FpcDlzakRlT1NNWnVvSTI4MVNZaC9GZmhuTGNLWXBZRnNTNG9nRnY5Q3RxeTBxWUVRVjNoMDUvTkV1ajRpN096ZW4wOHlBPT0iLCJtYWMiOiJkYmI3NTYwZDUxZWJjY2I0ZmUxMmQ2YmU2YzUxYmQ3MjYzMzJlZmQ1MjMyZTBhNGNlOTUyMTgxMDhjMzhlNjU1IiwidGFnIjoiIn0%3D; cookie_for_consent=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlVHb3RYN0M5L2FRa3ovaExFNWVXQ3c9PSIsInZhbHVlIjoiMFU4V0hvaGRDVXI5K3RzdzdkMjZ1eDZ3cnhXQy9nRUpxekg1bFIxa0VoUTBTY2x6TTB4SlBGL1lkbVpOV2IyRitMcGsyMHFOS2ZMalRDQWZzM1g0MThCcWpheHNMSE5QT2g4ZUhhNFRxTnNUL0ZuKzhyNnpUbXI1V3JMSEhlT0laT2hXN0JUWWd4MEhXMXBhTzhaZkh2VkR4TXF4NXlDNnpUbzcxMjdnL1RwUzduZ05BMy9iTnZDNTlEdlBWaW5RNDkraERQRXI3RmgzNzEzclFrTkQ5S21kRmNWK1I0OVhGUGx2cXpzK0tJdz0iLCJtYWMiOiJhZDg0MDlkNzAxNWI5MjBiN2NlZmE5ZDRkNDExNDg3MjdiMTY1MWY0NDdjNjZkNmI5ZDA1ZTE0MWIwNzc4M2E1IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik80dS9XaXFCWGMzZmZlbGFXVDByQXc9PSIsInZhbHVlIjoiRlpCNTArQ3RTTGw3aEhkVGNQajBiQlo3SjJLTWVEOVViRFFkbVZabmkyKzNYMjNxV2Y2aVNDTXpWK3liWGh5Q3JxUmZtTW8yR0hDRzBKbTdBd29FYisrbUI0UGo2c0c4TWRLaTVJL1l3WFNRQldPVnZ5bEp2QzBCVTB5VnZRZ3oiLCJtYWMiOiJlMmRjYTFhOTc3MTcxNDI2MWY2MWRlMmNjNTg4MzdhZWUxMTlhY2NlMzMxNDliZGVmNmRhNmYwOGJlYjA4ZjU4IiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6IndYSk1uVWpiVG5CaDZZZXUxaEhBbFE9PSIsInZhbHVlIjoiWW1tSy9TRHZYeDQ0WWVqWU1KeW9MQml4TXhTWlpjY0xxTnNhWEhVZGUrY0hJR0RMOG1wUTdFWlRvSUlaNDJYb2IrWFp6blhaUk5Zc2lSWEdFN3NvNW4rWmVXU0RPS2orc0dWaXZiVDk3bjhKeEZMdEZQeVJtS2tnUkZsTDd3T0EiLCJtYWMiOiI0MjczYWQyNTk0M2U3MzM1ZWI0YjAwNThmYjYzYzA5MjE2YmY4NTQwMmRlOWZhNzdjNDFlM2U0ZjE3ZjVkZGMwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-657939601\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1672837765 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>shaqi_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7a4e1a8bf2884ebcac0020a875b7168cc919bd88</span>\"\n  \"<span class=sf-dump-key>shaqi_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"346 characters\">{&quot;footprint&quot;:&quot;7a4e1a8bf2884ebcac0020a875b7168cc919bd88&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;tesmods.gc&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|IhQd5rv2OiHN31FogUE4N7nywrkbjngoKJ2fGSpGDJTc8ESMBnaVyTbMe1uM|$2y$12$Y0fKdUtNl.7uriE2jyqMteM5YHmP0ovxpe3RbMWYUS/IwySBzdTYK</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">21kNLtyrrz1EnHCRTflWSzE24GnCtMPJmB7j18C5</span>\"\n  \"<span class=sf-dump-key>shaqi_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">259NiGAyxSp79a8W1RViospfuj3oZ9d3ieXItl0L</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1672837765\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-914350149 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 17:45:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-914350149\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">21kNLtyrrz1EnHCRTflWSzE24GnCtMPJmB7j18C5</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">https://tesmods.gc/admin/ecommerce/options/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://tesmods.gc/admin/settings/license/verify?verified=true", "action_name": "settings.license.verify.index", "controller_action": "\\Shaqi\\ShaqiActivator\\Http\\Controllers\\ShaqiActivatorController@getVerifyLicense"}, "badge": null}}