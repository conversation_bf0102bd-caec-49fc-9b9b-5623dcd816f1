<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Shaqi\Ecommerce\Models\GlobalOption;
use <PERSON>haqi\Ecommerce\Models\GlobalOptionValue;
use <PERSON><PERSON>qi\Ecommerce\Models\Option;
use Shaqi\Ecommerce\Models\OptionValue;
use Shaqi\Ecommerce\Models\Product;
use Shaqi\Ecommerce\Models\ProductCategory;
use Shaqi\Ecommerce\Option\OptionType\Checkbox;
use Shaqi\Ecommerce\Option\OptionType\Dropdown;
use Shaqi\Ecommerce\Option\OptionType\RadioButton;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, create the global options if they don't exist
        $this->createGlobalOptions();

        // Then assign these options to products in relevant categories
        $this->assignOptionsToProducts();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the options from products (but keep global options)
        $this->removeOptionsFromProducts();
    }

    /**
     * Create the three global options: Painting, Paint Codes, Part Installation
     */
    private function createGlobalOptions(): void
    {
        $options = [
            [
                'name' => 'Painting',
                'option_type' => RadioButton::class,
                'required' => false,
                'values' => [
                    [
                        'option_value' => 'No Painting Required',
                        'affect_price' => 0,
                        'affect_type' => 0,
                        'order' => 0,
                    ],
                    [
                        'option_value' => 'Standard Paint Job',
                        'affect_price' => 150,
                        'affect_type' => 0,
                        'order' => 1,
                    ],
                    [
                        'option_value' => 'Premium Paint Job',
                        'affect_price' => 300,
                        'affect_type' => 0,
                        'order' => 2,
                    ],
                    [
                        'option_value' => 'Custom Paint Job',
                        'affect_price' => 500,
                        'affect_type' => 0,
                        'order' => 3,
                    ],
                ],
            ],
            [
                'name' => 'Paint Codes',
                'option_type' => Dropdown::class,
                'required' => false,
                'values' => [
                    [
                        'option_value' => 'Pearl White Multi-Coat (PPMR)',
                        'affect_price' => 0,
                        'affect_type' => 0,
                        'order' => 0,
                    ],
                    [
                        'option_value' => 'Solid Black (PBSB)',
                        'affect_price' => 0,
                        'affect_type' => 0,
                        'order' => 1,
                    ],
                    [
                        'option_value' => 'Midnight Silver Metallic (PMNG)',
                        'affect_price' => 0,
                        'affect_type' => 0,
                        'order' => 2,
                    ],
                    [
                        'option_value' => 'Deep Blue Metallic (PPSB)',
                        'affect_price' => 0,
                        'affect_type' => 0,
                        'order' => 3,
                    ],
                    [
                        'option_value' => 'Red Multi-Coat (PPMR)',
                        'affect_price' => 0,
                        'affect_type' => 0,
                        'order' => 4,
                    ],
                ],
            ],
            [
                'name' => 'Part Installation',
                'option_type' => RadioButton::class,
                'required' => false,
                'values' => [
                    [
                        'option_value' => 'Self Installation',
                        'affect_price' => 0,
                        'affect_type' => 0,
                        'order' => 0,
                    ],
                    [
                        'option_value' => 'Professional Installation',
                        'affect_price' => 100,
                        'affect_type' => 0,
                        'order' => 1,
                    ],
                    [
                        'option_value' => 'Installation + Alignment',
                        'affect_price' => 200,
                        'affect_type' => 0,
                        'order' => 2,
                    ],
                ],
            ],
        ];

        foreach ($options as $optionData) {
            // Check if global option already exists
            $existingOption = GlobalOption::where('name', $optionData['name'])->first();

            if (!$existingOption) {
                // Create the global option
                $globalOption = GlobalOption::create([
                    'name' => $optionData['name'],
                    'option_type' => $optionData['option_type'],
                    'required' => $optionData['required'],
                ]);

                // Create the option values
                foreach ($optionData['values'] as $valueData) {
                    GlobalOptionValue::create([
                        'option_id' => $globalOption->id,
                        'option_value' => $valueData['option_value'],
                        'affect_price' => $valueData['affect_price'],
                        'affect_type' => $valueData['affect_type'],
                        'order' => $valueData['order'],
                    ]);
                }
            }
        }
    }

    /**
     * Assign the global options to products in relevant categories
     */
    private function assignOptionsToProducts(): void
    {
        // Get the global options we just created
        $paintingOption = GlobalOption::where('name', 'Painting')->first();
        $paintCodesOption = GlobalOption::where('name', 'Paint Codes')->first();
        $partInstallationOption = GlobalOption::where('name', 'Part Installation')->first();

        if (!$paintingOption || !$paintCodesOption || !$partInstallationOption) {
            return; // Options not found, skip
        }

        // Define specific category IDs that should receive these options
        // Based on the actual database structure for Tesla parts
        $relevantCategoryIds = [
            // 1001 Bumper and Fascia categories (all Tesla models)
            34, 86, 144, 272,

            // 1010 Body Panels categories (all Tesla models)
            41, 87, 145, 273,

            // Specific subcategories for bumpers and fascia
            35, 39, 112, 115, 171, 174, 300, 305, // Front/Rear Bumper Fascia
            36, 114, 173, 302, // Front Grille and Applique
            37, 40, 113, 116, 172, 175, 303, 304, // Energy Absorbers

            // Specific subcategories for body panels
            42, 117, 176, 306, // Body Side Panels
            43, 118, 178, 308, // Closure Panels
            44, 119, 179, 311, // Front Fenders
            46, 121, 182, 315, // Rear End Panels
            124, 184, 318, // Roof Panels (if painting relevant)

            // Hood related categories (painting relevant)
            55, 131, 330, 333, // Hood Hinges and Fittings, Hood Latch and Release
        ];

        // Get products from these specific categories
        $productIds = collect();
        foreach ($relevantCategoryIds as $categoryId) {
            $categoryProductIds = DB::table('ec_product_category_product')
                ->where('category_id', $categoryId)
                ->pluck('product_id');
            $productIds = $productIds->merge($categoryProductIds);
        }

        // Remove duplicates
        $productIds = $productIds->unique();

        echo "Found " . $productIds->count() . " products in relevant categories.\n";

        // For each product, create individual options based on global options
        foreach ($productIds as $productId) {
            $this->createProductOption($productId, $paintingOption);
            $this->createProductOption($productId, $paintCodesOption);
            $this->createProductOption($productId, $partInstallationOption);
        }

        echo "Successfully added options to " . $productIds->count() . " products.\n";
    }

    /**
     * Create a product-specific option from a global option
     */
    private function createProductOption(int $productId, GlobalOption $globalOption): void
    {
        // Check if this product already has this option
        $existingOption = Option::where('product_id', $productId)
            ->where('name', $globalOption->name)
            ->first();

        if ($existingOption) {
            return; // Option already exists for this product
        }

        // Create the product option
        $productOption = Option::create([
            'name' => $globalOption->name,
            'option_type' => $globalOption->option_type,
            'product_id' => $productId,
            'order' => 9999,
            'required' => $globalOption->required,
        ]);

        // Copy the option values
        foreach ($globalOption->values as $globalValue) {
            OptionValue::create([
                'option_id' => $productOption->id,
                'option_value' => $globalValue->option_value,
                'affect_price' => $globalValue->affect_price,
                'affect_type' => $globalValue->affect_type,
                'order' => $globalValue->order,
            ]);
        }
    }

    /**
     * Remove the options from products (rollback)
     */
    private function removeOptionsFromProducts(): void
    {
        $optionNames = ['Painting', 'Paint Codes', 'Part Installation'];

        // Get all product options with these names
        $productOptions = Option::whereIn('name', $optionNames)->get();

        foreach ($productOptions as $option) {
            // Delete option values first
            $option->values()->delete();
            // Then delete the option
            $option->delete();
        }
    }
};
