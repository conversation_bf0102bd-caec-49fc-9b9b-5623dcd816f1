<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Shaqi\Ecommerce\Models\GlobalOption;
use Shaqi\Ecommerce\Models\Option;
use Shaqi\Ecommerce\Models\OptionValue;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Assign existing global options to products in relevant categories
        $this->assignOptionsToProducts();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the options from products (but keep global options)
        $this->removeOptionsFromProducts();
    }



    /**
     * Assign the existing global options to products in relevant categories
     */
    private function assignOptionsToProducts(): void
    {
        // Get the existing global options (created via admin panel)
        $paintingOption = GlobalOption::where('name', 'Painting')->first();
        $paintCodesOption = GlobalOption::where('name', 'Paint Codes')->first();
        $partInstallationOption = GlobalOption::where('name', 'Part Installation')->first();

        if (!$paintingOption || !$paintCodesOption || !$partInstallationOption) {
            echo "Required global options not found. Please create them in admin panel first.\n";
            echo "Expected options: Painting, Paint Codes, Part Installation\n";
            return;
        }

        echo "Found existing global options:\n";
        echo "- Painting (ID: {$paintingOption->id})\n";
        echo "- Paint Codes (ID: {$paintCodesOption->id})\n";
        echo "- Part Installation (ID: {$partInstallationOption->id})\n\n";

        // Define specific category IDs that should receive these options
        // Based on the actual database structure for Tesla parts
        // Starting with main categories first for testing
        $relevantCategoryIds = [
            // 1001 Bumper and Fascia categories (all Tesla models)
            34, 86, 144, 272,

            // 1010 Body Panels categories (all Tesla models)
            41, 87, 145, 273,
        ];

        // Get products from these specific categories
        $productIds = collect();
        foreach ($relevantCategoryIds as $categoryId) {
            $categoryProductIds = DB::table('ec_product_category_product')
                ->where('category_id', $categoryId)
                ->pluck('product_id');
            $productIds = $productIds->merge($categoryProductIds);
        }

        // Remove duplicates
        $productIds = $productIds->unique();

        echo "Found " . $productIds->count() . " products in relevant categories.\n";

        // For each product, create individual options based on global options
        foreach ($productIds as $productId) {
            $this->createProductOption($productId, $paintingOption);
            $this->createProductOption($productId, $paintCodesOption);
            $this->createProductOption($productId, $partInstallationOption);
        }

        echo "Successfully added options to " . $productIds->count() . " products.\n";
    }

    /**
     * Create a product-specific option from a global option
     */
    private function createProductOption(int $productId, GlobalOption $globalOption): void
    {
        // Check if this product already has this option
        $existingOption = Option::where('product_id', $productId)
            ->where('name', $globalOption->name)
            ->first();

        if ($existingOption) {
            return; // Option already exists for this product
        }

        // Create the product option
        $productOption = Option::create([
            'name' => $globalOption->name,
            'option_type' => $globalOption->option_type,
            'product_id' => $productId,
            'order' => 9999,
            'required' => $globalOption->required,
        ]);

        // Copy the option values
        foreach ($globalOption->values as $globalValue) {
            OptionValue::create([
                'option_id' => $productOption->id,
                'option_value' => $globalValue->option_value,
                'affect_price' => $globalValue->affect_price,
                'affect_type' => $globalValue->affect_type,
                'order' => $globalValue->order,
            ]);
        }
    }

    /**
     * Remove the options from products (rollback)
     */
    private function removeOptionsFromProducts(): void
    {
        $optionNames = ['Painting', 'Paint Codes', 'Part Installation'];

        // Get all product options with these names
        $productOptions = Option::whereIn('name', $optionNames)->get();

        foreach ($productOptions as $option) {
            // Delete option values first
            $option->values()->delete();
            // Then delete the option
            $option->delete();
        }
    }
};
