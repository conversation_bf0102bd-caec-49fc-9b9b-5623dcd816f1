{"__meta": {"id": "01K2FRZPN920VDWAJJ5BGXCG9T", "datetime": "2025-08-12 18:34:21", "utime": **********.73913, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": *********8.860703, "end": **********.739158, "duration": 2.8784549236297607, "duration_str": "2.88s", "measures": [{"label": "Booting", "start": *********8.860703, "relative_start": 0, "end": **********.791368, "relative_end": **********.791368, "duration": 0.****************, "duration_str": "931ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.791381, "relative_start": 0.****************, "end": **********.739161, "relative_end": 3.0994415283203125e-06, "duration": 1.****************, "duration_str": "1.95s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.815709, "relative_start": 0.****************, "end": **********.83307, "relative_end": **********.83307, "duration": 0.017360925674438477, "duration_str": "17.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: theme.ninico::views.page", "start": **********.891452, "relative_start": 1.****************, "end": **********.891452, "relative_end": **********.891452, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.shortcodes.simple-slider.index", "start": **********.914327, "relative_start": 1.***************, "end": **********.914327, "relative_end": **********.914327, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.shortcodes.simple-slider.styles.fashion", "start": **********.917024, "relative_start": 1.0563209056854248, "end": **********.917024, "relative_end": **********.917024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.shortcodes.simple-slider.includes.image", "start": **********.922135, "relative_start": 1.061432123184204, "end": **********.922135, "relative_end": **********.922135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.shortcodes.simple-slider.includes.image", "start": **********.945382, "relative_start": 1.084679126739502, "end": **********.945382, "relative_end": **********.945382, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.shortcodes.simple-slider.includes.image", "start": **********.953053, "relative_start": 1.0923500061035156, "end": **********.953053, "relative_end": **********.953053, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/shortcode::partials.lazy-loading-placeholder", "start": **********.956952, "relative_start": 1.0962491035461426, "end": **********.956952, "relative_end": **********.956952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/shortcode::partials.lazy-loading-placeholder", "start": **********.957757, "relative_start": 1.0970540046691895, "end": **********.957757, "relative_end": **********.957757, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.shortcodes.maintenance-services.index", "start": **********.967795, "relative_start": 1.1070919036865234, "end": **********.967795, "relative_end": **********.967795, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/shortcode::partials.lazy-loading-placeholder", "start": **********.060584, "relative_start": 1.1998810768127441, "end": **********.060584, "relative_end": **********.060584, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/shortcode::partials.lazy-loading-placeholder", "start": **********.061306, "relative_start": 1.2006030082702637, "end": **********.061306, "relative_end": **********.061306, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::layouts.full-width", "start": **********.061697, "relative_start": 1.2009940147399902, "end": **********.061697, "relative_end": **********.061697, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.headers.index", "start": **********.06228, "relative_start": 1.2015769481658936, "end": **********.06228, "relative_end": **********.06228, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.header-search-bar", "start": **********.062756, "relative_start": 1.2020530700683594, "end": **********.062756, "relative_end": **********.062756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.066066, "relative_start": 1.2053630352020264, "end": **********.066066, "relative_end": **********.066066, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.066991, "relative_start": 1.2062880992889404, "end": **********.066991, "relative_end": **********.066991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.067304, "relative_start": 1.2066009044647217, "end": **********.067304, "relative_end": **********.067304, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.067603, "relative_start": 1.2069001197814941, "end": **********.067603, "relative_end": **********.067603, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.067947, "relative_start": 1.2072439193725586, "end": **********.067947, "relative_end": **********.067947, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.068308, "relative_start": 1.2076051235198975, "end": **********.068308, "relative_end": **********.068308, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.068635, "relative_start": 1.2079319953918457, "end": **********.068635, "relative_end": **********.068635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.06894, "relative_start": 1.2082369327545166, "end": **********.06894, "relative_end": **********.06894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.069183, "relative_start": 1.2084801197052002, "end": **********.069183, "relative_end": **********.069183, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.069501, "relative_start": 1.2087979316711426, "end": **********.069501, "relative_end": **********.069501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.069776, "relative_start": 1.2090730667114258, "end": **********.069776, "relative_end": **********.069776, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.070042, "relative_start": 1.209338903427124, "end": **********.070042, "relative_end": **********.070042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.070303, "relative_start": 1.2095999717712402, "end": **********.070303, "relative_end": **********.070303, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.070562, "relative_start": 1.2098588943481445, "end": **********.070562, "relative_end": **********.070562, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.070852, "relative_start": 1.2101490497589111, "end": **********.070852, "relative_end": **********.070852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.071091, "relative_start": 1.210387945175171, "end": **********.071091, "relative_end": **********.071091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.071356, "relative_start": 1.2106530666351318, "end": **********.071356, "relative_end": **********.071356, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.071629, "relative_start": 1.2109260559082031, "end": **********.071629, "relative_end": **********.071629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.07196, "relative_start": 1.211256980895996, "end": **********.07196, "relative_end": **********.07196, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.072243, "relative_start": 1.2115399837493896, "end": **********.072243, "relative_end": **********.072243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.072522, "relative_start": 1.2118189334869385, "end": **********.072522, "relative_end": **********.072522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.0728, "relative_start": 1.212096929550171, "end": **********.0728, "relative_end": **********.0728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.073101, "relative_start": 1.2123980522155762, "end": **********.073101, "relative_end": **********.073101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.073359, "relative_start": 1.212656021118164, "end": **********.073359, "relative_end": **********.073359, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.073722, "relative_start": 1.2130188941955566, "end": **********.073722, "relative_end": **********.073722, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.07411, "relative_start": 1.213407039642334, "end": **********.07411, "relative_end": **********.07411, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.074462, "relative_start": 1.213758945465088, "end": **********.074462, "relative_end": **********.074462, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.074722, "relative_start": 1.2140190601348877, "end": **********.074722, "relative_end": **********.074722, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.074978, "relative_start": 1.2142751216888428, "end": **********.074978, "relative_end": **********.074978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.075244, "relative_start": 1.214540958404541, "end": **********.075244, "relative_end": **********.075244, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.07552, "relative_start": 1.2148170471191406, "end": **********.07552, "relative_end": **********.07552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.075838, "relative_start": 1.215135097503662, "end": **********.075838, "relative_end": **********.075838, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.076387, "relative_start": 1.215683937072754, "end": **********.076387, "relative_end": **********.076387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.076663, "relative_start": 1.2159600257873535, "end": **********.076663, "relative_end": **********.076663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.076947, "relative_start": 1.2162439823150635, "end": **********.076947, "relative_end": **********.076947, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.077235, "relative_start": 1.2165319919586182, "end": **********.077235, "relative_end": **********.077235, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.077571, "relative_start": 1.2168679237365723, "end": **********.077571, "relative_end": **********.077571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.077955, "relative_start": 1.2172520160675049, "end": **********.077955, "relative_end": **********.077955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.078289, "relative_start": 1.2175860404968262, "end": **********.078289, "relative_end": **********.078289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.078566, "relative_start": 1.2178630828857422, "end": **********.078566, "relative_end": **********.078566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.078837, "relative_start": 1.2181339263916016, "end": **********.078837, "relative_end": **********.078837, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.079131, "relative_start": 1.2184278964996338, "end": **********.079131, "relative_end": **********.079131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.07941, "relative_start": 1.2187070846557617, "end": **********.07941, "relative_end": **********.07941, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.079711, "relative_start": 1.219007968902588, "end": **********.079711, "relative_end": **********.079711, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.079959, "relative_start": 1.2192559242248535, "end": **********.079959, "relative_end": **********.079959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.080238, "relative_start": 1.2195351123809814, "end": **********.080238, "relative_end": **********.080238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.080632, "relative_start": 1.2199289798736572, "end": **********.080632, "relative_end": **********.080632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.080987, "relative_start": 1.2202839851379395, "end": **********.080987, "relative_end": **********.080987, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.081255, "relative_start": 1.2205519676208496, "end": **********.081255, "relative_end": **********.081255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.08151, "relative_start": 1.2208070755004883, "end": **********.08151, "relative_end": **********.08151, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.081764, "relative_start": 1.2210609912872314, "end": **********.081764, "relative_end": **********.081764, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.082061, "relative_start": 1.221358060836792, "end": **********.082061, "relative_end": **********.082061, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.082371, "relative_start": 1.221668004989624, "end": **********.082371, "relative_end": **********.082371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.082678, "relative_start": 1.2219750881195068, "end": **********.082678, "relative_end": **********.082678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.082984, "relative_start": 1.2222809791564941, "end": **********.082984, "relative_end": **********.082984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.083241, "relative_start": 1.2225379943847656, "end": **********.083241, "relative_end": **********.083241, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.083528, "relative_start": 1.222825050354004, "end": **********.083528, "relative_end": **********.083528, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.083811, "relative_start": 1.2231080532073975, "end": **********.083811, "relative_end": **********.083811, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.08409, "relative_start": 1.2233870029449463, "end": **********.08409, "relative_end": **********.08409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.084367, "relative_start": 1.2236640453338623, "end": **********.084367, "relative_end": **********.084367, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.08469, "relative_start": 1.223987102508545, "end": **********.08469, "relative_end": **********.08469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.084986, "relative_start": 1.22428297996521, "end": **********.084986, "relative_end": **********.084986, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.085293, "relative_start": 1.2245900630950928, "end": **********.085293, "relative_end": **********.085293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.085561, "relative_start": 1.224858045578003, "end": **********.085561, "relative_end": **********.085561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.085828, "relative_start": 1.2251250743865967, "end": **********.085828, "relative_end": **********.085828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.086116, "relative_start": 1.2254130840301514, "end": **********.086116, "relative_end": **********.086116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.086354, "relative_start": 1.2256510257720947, "end": **********.086354, "relative_end": **********.086354, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.086621, "relative_start": 1.2259180545806885, "end": **********.086621, "relative_end": **********.086621, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.086883, "relative_start": 1.226180076599121, "end": **********.086883, "relative_end": **********.086883, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.087149, "relative_start": 1.2264459133148193, "end": **********.087149, "relative_end": **********.087149, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.08741, "relative_start": 1.2267069816589355, "end": **********.08741, "relative_end": **********.08741, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.087674, "relative_start": 1.226970911026001, "end": **********.087674, "relative_end": **********.087674, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.087933, "relative_start": 1.2272300720214844, "end": **********.087933, "relative_end": **********.087933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.088196, "relative_start": 1.2274930477142334, "end": **********.088196, "relative_end": **********.088196, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.08852, "relative_start": 1.2278170585632324, "end": **********.08852, "relative_end": **********.08852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.088756, "relative_start": 1.228053092956543, "end": **********.088756, "relative_end": **********.088756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.089037, "relative_start": 1.2283339500427246, "end": **********.089037, "relative_end": **********.089037, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.089271, "relative_start": 1.2285680770874023, "end": **********.089271, "relative_end": **********.089271, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.089539, "relative_start": 1.2288360595703125, "end": **********.089539, "relative_end": **********.089539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.089863, "relative_start": 1.2291600704193115, "end": **********.089863, "relative_end": **********.089863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.090236, "relative_start": 1.2295329570770264, "end": **********.090236, "relative_end": **********.090236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.090606, "relative_start": 1.229902982711792, "end": **********.090606, "relative_end": **********.090606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.090895, "relative_start": 1.230191946029663, "end": **********.090895, "relative_end": **********.090895, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.091152, "relative_start": 1.2304489612579346, "end": **********.091152, "relative_end": **********.091152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.091405, "relative_start": 1.2307019233703613, "end": **********.091405, "relative_end": **********.091405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.091708, "relative_start": 1.2310049533843994, "end": **********.091708, "relative_end": **********.091708, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.092044, "relative_start": 1.2313411235809326, "end": **********.092044, "relative_end": **********.092044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.092355, "relative_start": 1.231652021408081, "end": **********.092355, "relative_end": **********.092355, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.092642, "relative_start": 1.2319390773773193, "end": **********.092642, "relative_end": **********.092642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.092983, "relative_start": 1.2322800159454346, "end": **********.092983, "relative_end": **********.092983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.093275, "relative_start": 1.232572078704834, "end": **********.093275, "relative_end": **********.093275, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.093642, "relative_start": 1.2329390048980713, "end": **********.093642, "relative_end": **********.093642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.094046, "relative_start": 1.2333431243896484, "end": **********.094046, "relative_end": **********.094046, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.094492, "relative_start": 1.2337889671325684, "end": **********.094492, "relative_end": **********.094492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.094761, "relative_start": 1.234057903289795, "end": **********.094761, "relative_end": **********.094761, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.095041, "relative_start": 1.2343380451202393, "end": **********.095041, "relative_end": **********.095041, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.095333, "relative_start": 1.2346301078796387, "end": **********.095333, "relative_end": **********.095333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.095604, "relative_start": 1.234900951385498, "end": **********.095604, "relative_end": **********.095604, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.095879, "relative_start": 1.2351760864257812, "end": **********.095879, "relative_end": **********.095879, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.096155, "relative_start": 1.2354519367218018, "end": **********.096155, "relative_end": **********.096155, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.096428, "relative_start": 1.235724925994873, "end": **********.096428, "relative_end": **********.096428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.096694, "relative_start": 1.2359910011291504, "end": **********.096694, "relative_end": **********.096694, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.096967, "relative_start": 1.2362639904022217, "end": **********.096967, "relative_end": **********.096967, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.097254, "relative_start": 1.23655104637146, "end": **********.097254, "relative_end": **********.097254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.097491, "relative_start": 1.236788034439087, "end": **********.097491, "relative_end": **********.097491, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.097756, "relative_start": 1.2370529174804688, "end": **********.097756, "relative_end": **********.097756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.098015, "relative_start": 1.2373120784759521, "end": **********.098015, "relative_end": **********.098015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.098276, "relative_start": 1.2375729084014893, "end": **********.098276, "relative_end": **********.098276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.098534, "relative_start": 1.2378311157226562, "end": **********.098534, "relative_end": **********.098534, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.098797, "relative_start": 1.2380940914154053, "end": **********.098797, "relative_end": **********.098797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.099055, "relative_start": 1.2383520603179932, "end": **********.099055, "relative_end": **********.099055, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.09936, "relative_start": 1.238656997680664, "end": **********.09936, "relative_end": **********.09936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.099647, "relative_start": 1.2389440536499023, "end": **********.099647, "relative_end": **********.099647, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.099933, "relative_start": 1.2392299175262451, "end": **********.099933, "relative_end": **********.099933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.100168, "relative_start": 1.2394649982452393, "end": **********.100168, "relative_end": **********.100168, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.10044, "relative_start": 1.2397370338439941, "end": **********.10044, "relative_end": **********.10044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.100704, "relative_start": 1.2400009632110596, "end": **********.100704, "relative_end": **********.100704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.nested-select-option", "start": **********.101077, "relative_start": 1.2403740882873535, "end": **********.101077, "relative_end": **********.101077, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.headers.default", "start": **********.102328, "relative_start": 1.2416250705718994, "end": **********.102328, "relative_end": **********.102328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.header-top", "start": **********.102764, "relative_start": 1.242060899734497, "end": **********.102764, "relative_end": **********.102764, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.currency-switcher", "start": **********.119575, "relative_start": 1.2588720321655273, "end": **********.119575, "relative_end": **********.119575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.language-switcher", "start": **********.120141, "relative_start": 1.2594380378723145, "end": **********.120141, "relative_end": **********.120141, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0226480071dd0e74b211507465ab2265", "start": **********.125212, "relative_start": 1.2645089626312256, "end": **********.125212, "relative_end": **********.125212, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6112de9dcda50c4e5e8edc1897f5a562", "start": **********.126305, "relative_start": 1.2656021118164062, "end": **********.126305, "relative_end": **********.126305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::08a04217c3e416224df63d8e9dd782a7", "start": **********.127561, "relative_start": 1.2668581008911133, "end": **********.127561, "relative_end": **********.127561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3fb0a03fd317b61c1c5c6c5fdfca9fed", "start": **********.128582, "relative_start": 1.2678790092468262, "end": **********.128582, "relative_end": **********.128582, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::19d2357ba0f51b29e340109a50dc8d90", "start": **********.129437, "relative_start": 1.2687339782714844, "end": **********.129437, "relative_end": **********.129437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.logo", "start": **********.129864, "relative_start": 1.2691609859466553, "end": **********.129864, "relative_end": **********.129864, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.245637, "relative_start": 1.3849339485168457, "end": **********.245637, "relative_end": **********.245637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.253487, "relative_start": 1.3927841186523438, "end": **********.253487, "relative_end": **********.253487, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.26008, "relative_start": 1.3993771076202393, "end": **********.26008, "relative_end": **********.26008, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.270899, "relative_start": 1.41019606590271, "end": **********.270899, "relative_end": **********.270899, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.286457, "relative_start": 1.4257540702819824, "end": **********.286457, "relative_end": **********.286457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.301812, "relative_start": 1.4411089420318604, "end": **********.301812, "relative_end": **********.301812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.320078, "relative_start": 1.4593749046325684, "end": **********.320078, "relative_end": **********.320078, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.331025, "relative_start": 1.4703218936920166, "end": **********.331025, "relative_end": **********.331025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.338508, "relative_start": 1.4778048992156982, "end": **********.338508, "relative_end": **********.338508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.350803, "relative_start": 1.4900999069213867, "end": **********.350803, "relative_end": **********.350803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.368799, "relative_start": 1.5080959796905518, "end": **********.368799, "relative_end": **********.368799, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.390468, "relative_start": 1.5297648906707764, "end": **********.390468, "relative_end": **********.390468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.417789, "relative_start": 1.5570859909057617, "end": **********.417789, "relative_end": **********.417789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.431601, "relative_start": 1.5708980560302734, "end": **********.431601, "relative_end": **********.431601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.454554, "relative_start": 1.593851089477539, "end": **********.454554, "relative_end": **********.454554, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.503089, "relative_start": 1.6423859596252441, "end": **********.503089, "relative_end": **********.503089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.507554, "relative_start": 1.6468510627746582, "end": **********.507554, "relative_end": **********.507554, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.515974, "relative_start": 1.655271053314209, "end": **********.515974, "relative_end": **********.515974, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.527712, "relative_start": 1.6670091152191162, "end": **********.527712, "relative_end": **********.527712, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.539155, "relative_start": 1.6784520149230957, "end": **********.539155, "relative_end": **********.539155, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.552172, "relative_start": 1.6914689540863037, "end": **********.552172, "relative_end": **********.552172, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.557533, "relative_start": 1.6968300342559814, "end": **********.557533, "relative_end": **********.557533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.566717, "relative_start": 1.7060139179229736, "end": **********.566717, "relative_end": **********.566717, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.587254, "relative_start": 1.7265510559082031, "end": **********.587254, "relative_end": **********.587254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.618997, "relative_start": 1.7582941055297852, "end": **********.618997, "relative_end": **********.618997, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.703867, "relative_start": 1.8431639671325684, "end": **********.703867, "relative_end": **********.703867, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.77107, "relative_start": 1.9103670120239258, "end": **********.77107, "relative_end": **********.77107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.796181, "relative_start": 1.9354779720306396, "end": **********.796181, "relative_end": **********.796181, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.830166, "relative_start": 1.9694631099700928, "end": **********.830166, "relative_end": **********.830166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.header-meta", "start": **********.872541, "relative_start": 2.011837959289551, "end": **********.872541, "relative_end": **********.872541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.navbar", "start": **********.8752, "relative_start": 2.0144970417022705, "end": **********.8752, "relative_end": **********.8752, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.header-middle", "start": **********.87689, "relative_start": 2.0161869525909424, "end": **********.87689, "relative_end": **********.87689, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.logo", "start": **********.877627, "relative_start": 2.0169239044189453, "end": **********.877627, "relative_end": **********.877627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.header-meta", "start": **********.881569, "relative_start": 2.0208659172058105, "end": **********.881569, "relative_end": **********.881569, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.logo", "start": **********.882725, "relative_start": 2.022022008895874, "end": **********.882725, "relative_end": **********.882725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.logo", "start": **********.88709, "relative_start": 2.0263869762420654, "end": **********.88709, "relative_end": **********.88709, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.mobile.menu-tab-content", "start": **********.891876, "relative_start": 2.03117299079895, "end": **********.891876, "relative_end": **********.891876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.mobile-menu", "start": **********.911908, "relative_start": 2.0512049198150635, "end": **********.911908, "relative_end": **********.911908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.921704, "relative_start": 2.0610010623931885, "end": **********.921704, "relative_end": **********.921704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.93206, "relative_start": 2.071357011795044, "end": **********.93206, "relative_end": **********.93206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.938696, "relative_start": 2.0779929161071777, "end": **********.938696, "relative_end": **********.938696, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.953538, "relative_start": 2.092834949493408, "end": **********.953538, "relative_end": **********.953538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.962759, "relative_start": 2.1020560264587402, "end": **********.962759, "relative_end": **********.962759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.981858, "relative_start": 2.121155023574829, "end": **********.981858, "relative_end": **********.981858, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.990029, "relative_start": 2.129326105117798, "end": **********.990029, "relative_end": **********.990029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.998341, "relative_start": 2.1376380920410156, "end": **********.998341, "relative_end": **********.998341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.002563, "relative_start": 2.141860008239746, "end": **********.002563, "relative_end": **********.002563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.013152, "relative_start": 2.152448892593384, "end": **********.013152, "relative_end": **********.013152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.02695, "relative_start": 2.1662468910217285, "end": **********.02695, "relative_end": **********.02695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.045011, "relative_start": 2.1843080520629883, "end": **********.045011, "relative_end": **********.045011, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.056539, "relative_start": 2.195836067199707, "end": **********.056539, "relative_end": **********.056539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.07335, "relative_start": 2.212646961212158, "end": **********.07335, "relative_end": **********.07335, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.101925, "relative_start": 2.2412219047546387, "end": **********.101925, "relative_end": **********.101925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.105581, "relative_start": 2.244878053665161, "end": **********.105581, "relative_end": **********.105581, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.10878, "relative_start": 2.248076915740967, "end": **********.10878, "relative_end": **********.10878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.115221, "relative_start": 2.2545180320739746, "end": **********.115221, "relative_end": **********.115221, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.122263, "relative_start": 2.2615599632263184, "end": **********.122263, "relative_end": **********.122263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.135497, "relative_start": 2.274794101715088, "end": **********.135497, "relative_end": **********.135497, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.145662, "relative_start": 2.284959077835083, "end": **********.145662, "relative_end": **********.145662, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.153257, "relative_start": 2.2925539016723633, "end": **********.153257, "relative_end": **********.153257, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.176533, "relative_start": 2.3158299922943115, "end": **********.176533, "relative_end": **********.176533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.215478, "relative_start": 2.3547749519348145, "end": **********.215478, "relative_end": **********.215478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.258848, "relative_start": 2.3981449604034424, "end": **********.258848, "relative_end": **********.258848, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.296077, "relative_start": 2.4353740215301514, "end": **********.296077, "relative_end": **********.296077, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.304327, "relative_start": 2.4436240196228027, "end": **********.304327, "relative_end": **********.304327, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.323319, "relative_start": 2.462615966796875, "end": **********.323319, "relative_end": **********.323319, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.language-switcher", "start": **********.367608, "relative_start": 2.5069050788879395, "end": **********.367608, "relative_end": **********.367608, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.currency-switcher", "start": **********.368253, "relative_start": 2.507550001144409, "end": **********.368253, "relative_end": **********.368253, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::views.ecommerce.includes.mini-cart", "start": **********.369105, "relative_start": 2.508402109146118, "end": **********.369105, "relative_end": **********.369105, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.mobile.categories-tab-content", "start": **********.371533, "relative_start": 2.5108299255371094, "end": **********.371533, "relative_end": **********.371533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.categories-dropdown-mobile", "start": **********.372128, "relative_start": 2.511425018310547, "end": **********.372128, "relative_end": **********.372128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.footer", "start": **********.378857, "relative_start": 2.5181539058685303, "end": **********.378857, "relative_end": **********.378857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::/../widgets/site-info/templates.frontend", "start": **********.38687, "relative_start": 2.5261669158935547, "end": **********.38687, "relative_end": **********.38687, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::/../widgets/custom-menu/templates.frontend", "start": **********.391389, "relative_start": 2.5306859016418457, "end": **********.391389, "relative_end": **********.391389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.footer-menu", "start": **********.400316, "relative_start": 2.5396130084991455, "end": **********.400316, "relative_end": **********.400316, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::/../widgets/custom-menu/templates.frontend", "start": **********.427147, "relative_start": 2.566443920135498, "end": **********.427147, "relative_end": **********.427147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.footer-menu", "start": **********.432238, "relative_start": 2.571535110473633, "end": **********.432238, "relative_end": **********.432238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::/../widgets/custom-menu/templates.frontend", "start": **********.482214, "relative_start": 2.6215109825134277, "end": **********.482214, "relative_end": **********.482214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.footer-menu", "start": **********.490904, "relative_start": 2.6302011013031006, "end": **********.490904, "relative_end": **********.490904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::31ae887b48bc7956b164b04a31b206f3", "start": **********.503562, "relative_start": 2.6428589820861816, "end": **********.503562, "relative_end": **********.503562, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1001eded5b0608646935941ff416e315", "start": **********.510736, "relative_start": 2.6500329971313477, "end": **********.510736, "relative_end": **********.510736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::950abe7b40c10237079259e7602acfec", "start": **********.514396, "relative_start": 2.6536929607391357, "end": **********.514396, "relative_end": **********.514396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7e87b9b6cf2d20df0f49b400032abd2c", "start": **********.51752, "relative_start": 2.6568169593811035, "end": **********.51752, "relative_end": **********.51752, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f418e702bf973d5917d32a22532847e3", "start": **********.521522, "relative_start": 2.6608190536499023, "end": **********.521522, "relative_end": **********.521522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::/../widgets/newsletter/templates.frontend", "start": **********.5375, "relative_start": 2.6767969131469727, "end": **********.5375, "relative_end": **********.5375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.560089, "relative_start": 2.6993861198425293, "end": **********.560089, "relative_end": **********.560089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.form-content-only", "start": **********.563561, "relative_start": 2.7028579711914062, "end": **********.563561, "relative_end": **********.563561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.564904, "relative_start": 2.7042009830474854, "end": **********.564904, "relative_end": **********.564904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.field", "start": **********.566773, "relative_start": 2.7060699462890625, "end": **********.566773, "relative_end": **********.566773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.567701, "relative_start": 2.706998109817505, "end": **********.567701, "relative_end": **********.567701, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.568422, "relative_start": 2.707719087600708, "end": **********.568422, "relative_end": **********.568422, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.570204, "relative_start": 2.709501028060913, "end": **********.570204, "relative_end": **********.570204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.570626, "relative_start": 2.709923028945923, "end": **********.570626, "relative_end": **********.570626, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.field", "start": **********.570966, "relative_start": 2.7102630138397217, "end": **********.570966, "relative_end": **********.570966, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.571409, "relative_start": 2.7107059955596924, "end": **********.571409, "relative_end": **********.571409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.571625, "relative_start": 2.7109220027923584, "end": **********.571625, "relative_end": **********.571625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.571896, "relative_start": 2.711193084716797, "end": **********.571896, "relative_end": **********.571896, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.email", "start": **********.572333, "relative_start": 2.71163010597229, "end": **********.572333, "relative_end": **********.572333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.field", "start": **********.572817, "relative_start": 2.712114095687866, "end": **********.572817, "relative_end": **********.572817, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.573256, "relative_start": 2.712553024291992, "end": **********.573256, "relative_end": **********.573256, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.573469, "relative_start": 2.71276593208313, "end": **********.573469, "relative_end": **********.573469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.573742, "relative_start": 2.713038921356201, "end": **********.573742, "relative_end": **********.573742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.574044, "relative_start": 2.713340997695923, "end": **********.574044, "relative_end": **********.574044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.field", "start": **********.574361, "relative_start": 2.713658094406128, "end": **********.574361, "relative_end": **********.574361, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.574772, "relative_start": 2.71406888961792, "end": **********.574772, "relative_end": **********.574772, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.574981, "relative_start": 2.714277982711792, "end": **********.574981, "relative_end": **********.574981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.575221, "relative_start": 2.7145180702209473, "end": **********.575221, "relative_end": **********.575221, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.575497, "relative_start": 2.7147939205169678, "end": **********.575497, "relative_end": **********.575497, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.field", "start": **********.575803, "relative_start": 2.715100049972534, "end": **********.575803, "relative_end": **********.575803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.576224, "relative_start": 2.7155210971832275, "end": **********.576224, "relative_end": **********.576224, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.576439, "relative_start": 2.715735912322998, "end": **********.576439, "relative_end": **********.576439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.576713, "relative_start": 2.716010093688965, "end": **********.576713, "relative_end": **********.576713, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.577211, "relative_start": 2.716507911682129, "end": **********.577211, "relative_end": **********.577211, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.field", "start": **********.577973, "relative_start": 2.7172698974609375, "end": **********.577973, "relative_end": **********.577973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.578802, "relative_start": 2.7180991172790527, "end": **********.578802, "relative_end": **********.578802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.579165, "relative_start": 2.7184619903564453, "end": **********.579165, "relative_end": **********.579165, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.579625, "relative_start": 2.7189218997955322, "end": **********.579625, "relative_end": **********.579625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.580156, "relative_start": 2.7194530963897705, "end": **********.580156, "relative_end": **********.580156, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.field", "start": **********.580662, "relative_start": 2.719959020614624, "end": **********.580662, "relative_end": **********.580662, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.581383, "relative_start": 2.720679998397827, "end": **********.581383, "relative_end": **********.581383, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.581761, "relative_start": 2.721057891845703, "end": **********.581761, "relative_end": **********.581761, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.582092, "relative_start": 2.721389055252075, "end": **********.582092, "relative_end": **********.582092, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-form-builder::button", "start": **********.582836, "relative_start": 2.722132921218872, "end": **********.582836, "relative_end": **********.582836, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.583646, "relative_start": 2.722943067550659, "end": **********.583646, "relative_end": **********.583646, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.58665, "relative_start": 2.7259469032287598, "end": **********.58665, "relative_end": **********.58665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::/../widgets/cta-contact/templates.frontend", "start": **********.591551, "relative_start": 2.7308480739593506, "end": **********.591551, "relative_end": **********.591551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::/../widgets/reviews-logos/templates.frontend", "start": **********.595393, "relative_start": 2.734689950942993, "end": **********.595393, "relative_end": **********.595393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::layouts.base", "start": **********.597903, "relative_start": 2.7372000217437744, "end": **********.597903, "relative_end": **********.597903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::partials.header", "start": **********.623836, "relative_start": 2.7631330490112305, "end": **********.623836, "relative_end": **********.623836, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language::partials.hreflang", "start": **********.669816, "relative_start": 2.809113025665283, "end": **********.669816, "relative_end": **********.669816, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.scroll-top", "start": **********.676172, "relative_start": 2.8154690265655518, "end": **********.676172, "relative_end": **********.676172, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.navigation-bar", "start": **********.685128, "relative_start": 2.824424982070923, "end": **********.685128, "relative_end": **********.685128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::partials.footer", "start": **********.690782, "relative_start": 2.8300790786743164, "end": **********.690782, "relative_end": **********.690782, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::admin-bar", "start": **********.70352, "relative_start": 2.8428170680999756, "end": **********.70352, "relative_end": **********.70352, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::fronts.toast-notification", "start": **********.718272, "relative_start": 2.8575689792633057, "end": **********.718272, "relative_end": **********.718272, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/shortcode::partials.lazy-loading-script", "start": **********.721225, "relative_start": 2.8605220317840576, "end": **********.721225, "relative_end": **********.721225, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/shortcode::partials.lazy-loading-script", "start": **********.722464, "relative_start": 2.8617610931396484, "end": **********.722464, "relative_end": **********.722464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/shortcode::partials.lazy-loading-script", "start": **********.722792, "relative_start": 2.862088918685913, "end": **********.722792, "relative_end": **********.722792, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/shortcode::partials.lazy-loading-script", "start": **********.723088, "relative_start": 2.8623850345611572, "end": **********.723088, "relative_end": **********.723088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.734599, "relative_start": 2.8738961219787598, "end": **********.734895, "relative_end": **********.734895, "duration": 0.00029587745666503906, "duration_str": "296μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 59299192, "peak_usage_str": "57MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "lcoal", "Debug Mode": "Enabled", "URL": "tesmods.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 281, "nb_templates": 281, "templates": [{"name": "1x theme.ninico::views.page", "param_count": null, "params": [], "start": **********.891424, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/views/page.blade.phptheme.ninico::views.page", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fviews%2Fpage.blade.php&line=1", "ajax": false, "filename": "page.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::views.page"}, {"name": "1x theme.ninico::partials.shortcodes.simple-slider.index", "param_count": null, "params": [], "start": **********.914304, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/shortcodes/simple-slider/index.blade.phptheme.ninico::partials.shortcodes.simple-slider.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fshortcodes%2Fsimple-slider%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.shortcodes.simple-slider.index"}, {"name": "1x theme.ninico::partials.shortcodes.simple-slider.styles.fashion", "param_count": null, "params": [], "start": **********.917003, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/shortcodes/simple-slider/styles/fashion.blade.phptheme.ninico::partials.shortcodes.simple-slider.styles.fashion", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fshortcodes%2Fsimple-slider%2Fstyles%2Ffashion.blade.php&line=1", "ajax": false, "filename": "fashion.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.shortcodes.simple-slider.styles.fashion"}, {"name": "3x theme.ninico::partials.shortcodes.simple-slider.includes.image", "param_count": null, "params": [], "start": **********.922114, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/shortcodes/simple-slider/includes/image.blade.phptheme.ninico::partials.shortcodes.simple-slider.includes.image", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fshortcodes%2Fsimple-slider%2Fincludes%2Fimage.blade.php&line=1", "ajax": false, "filename": "image.blade.php", "line": "?"}, "render_count": 3, "name_original": "theme.ninico::partials.shortcodes.simple-slider.includes.image"}, {"name": "4x packages/shortcode::partials.lazy-loading-placeholder", "param_count": null, "params": [], "start": **********.956931, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/packages/shortcode/resources/views/partials/lazy-loading-placeholder.blade.phppackages/shortcode::partials.lazy-loading-placeholder", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Fshortcode%2Fresources%2Fviews%2Fpartials%2Flazy-loading-placeholder.blade.php&line=1", "ajax": false, "filename": "lazy-loading-placeholder.blade.php", "line": "?"}, "render_count": 4, "name_original": "packages/shortcode::partials.lazy-loading-placeholder"}, {"name": "1x theme.ninico::partials.shortcodes.maintenance-services.index", "param_count": null, "params": [], "start": **********.96777, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/shortcodes/maintenance-services/index.blade.phptheme.ninico::partials.shortcodes.maintenance-services.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fshortcodes%2Fmaintenance-services%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.shortcodes.maintenance-services.index"}, {"name": "1x theme.ninico::layouts.full-width", "param_count": null, "params": [], "start": **********.061677, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/layouts/full-width.blade.phptheme.ninico::layouts.full-width", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Flayouts%2Ffull-width.blade.php&line=1", "ajax": false, "filename": "full-width.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::layouts.full-width"}, {"name": "1x theme.ninico::partials.headers.index", "param_count": null, "params": [], "start": **********.062259, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/headers/index.blade.phptheme.ninico::partials.headers.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fheaders%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.headers.index"}, {"name": "1x theme.ninico::partials.header-search-bar", "param_count": null, "params": [], "start": **********.062735, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/header-search-bar.blade.phptheme.ninico::partials.header-search-bar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fheader-search-bar.blade.php&line=1", "ajax": false, "filename": "header-search-bar.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.header-search-bar"}, {"name": "118x core/base::forms.partials.nested-select-option", "param_count": null, "params": [], "start": **********.06603, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/partials/nested-select-option.blade.phpcore/base::forms.partials.nested-select-option", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fnested-select-option.blade.php&line=1", "ajax": false, "filename": "nested-select-option.blade.php", "line": "?"}, "render_count": 118, "name_original": "core/base::forms.partials.nested-select-option"}, {"name": "1x theme.ninico::partials.headers.default", "param_count": null, "params": [], "start": **********.102306, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/headers/default.blade.phptheme.ninico::partials.headers.default", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fheaders%2Fdefault.blade.php&line=1", "ajax": false, "filename": "default.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.headers.default"}, {"name": "1x theme.ninico::partials.header-top", "param_count": null, "params": [], "start": **********.102745, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/header-top.blade.phptheme.ninico::partials.header-top", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fheader-top.blade.php&line=1", "ajax": false, "filename": "header-top.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.header-top"}, {"name": "2x theme.ninico::partials.currency-switcher", "param_count": null, "params": [], "start": **********.119553, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/currency-switcher.blade.phptheme.ninico::partials.currency-switcher", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fcurrency-switcher.blade.php&line=1", "ajax": false, "filename": "currency-switcher.blade.php", "line": "?"}, "render_count": 2, "name_original": "theme.ninico::partials.currency-switcher"}, {"name": "2x theme.ninico::partials.language-switcher", "param_count": null, "params": [], "start": **********.120123, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/language-switcher.blade.phptheme.ninico::partials.language-switcher", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Flanguage-switcher.blade.php&line=1", "ajax": false, "filename": "language-switcher.blade.php", "line": "?"}, "render_count": 2, "name_original": "theme.ninico::partials.language-switcher"}, {"name": "1x __components::0226480071dd0e74b211507465ab2265", "param_count": null, "params": [], "start": **********.125193, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/0226480071dd0e74b211507465ab2265.blade.php__components::0226480071dd0e74b211507465ab2265", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F0226480071dd0e74b211507465ab2265.blade.php&line=1", "ajax": false, "filename": "0226480071dd0e74b211507465ab2265.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0226480071dd0e74b211507465ab2265"}, {"name": "1x __components::6112de9dcda50c4e5e8edc1897f5a562", "param_count": null, "params": [], "start": **********.12627, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/6112de9dcda50c4e5e8edc1897f5a562.blade.php__components::6112de9dcda50c4e5e8edc1897f5a562", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F6112de9dcda50c4e5e8edc1897f5a562.blade.php&line=1", "ajax": false, "filename": "6112de9dcda50c4e5e8edc1897f5a562.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::6112de9dcda50c4e5e8edc1897f5a562"}, {"name": "1x __components::08a04217c3e416224df63d8e9dd782a7", "param_count": null, "params": [], "start": **********.127538, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/08a04217c3e416224df63d8e9dd782a7.blade.php__components::08a04217c3e416224df63d8e9dd782a7", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F08a04217c3e416224df63d8e9dd782a7.blade.php&line=1", "ajax": false, "filename": "08a04217c3e416224df63d8e9dd782a7.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::08a04217c3e416224df63d8e9dd782a7"}, {"name": "1x __components::3fb0a03fd317b61c1c5c6c5fdfca9fed", "param_count": null, "params": [], "start": **********.128563, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/3fb0a03fd317b61c1c5c6c5fdfca9fed.blade.php__components::3fb0a03fd317b61c1c5c6c5fdfca9fed", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F3fb0a03fd317b61c1c5c6c5fdfca9fed.blade.php&line=1", "ajax": false, "filename": "3fb0a03fd317b61c1c5c6c5fdfca9fed.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3fb0a03fd317b61c1c5c6c5fdfca9fed"}, {"name": "1x __components::19d2357ba0f51b29e340109a50dc8d90", "param_count": null, "params": [], "start": **********.129417, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/19d2357ba0f51b29e340109a50dc8d90.blade.php__components::19d2357ba0f51b29e340109a50dc8d90", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F19d2357ba0f51b29e340109a50dc8d90.blade.php&line=1", "ajax": false, "filename": "19d2357ba0f51b29e340109a50dc8d90.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::19d2357ba0f51b29e340109a50dc8d90"}, {"name": "4x theme.ninico::partials.logo", "param_count": null, "params": [], "start": **********.129836, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/logo.blade.phptheme.ninico::partials.logo", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}, "render_count": 4, "name_original": "theme.ninico::partials.logo"}, {"name": "57x theme.ninico::partials.menu", "param_count": null, "params": [], "start": **********.245613, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.phptheme.ninico::partials.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 57, "name_original": "theme.ninico::partials.menu"}, {"name": "2x theme.ninico::partials.header-meta", "param_count": null, "params": [], "start": **********.872518, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/header-meta.blade.phptheme.ninico::partials.header-meta", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fheader-meta.blade.php&line=1", "ajax": false, "filename": "header-meta.blade.php", "line": "?"}, "render_count": 2, "name_original": "theme.ninico::partials.header-meta"}, {"name": "1x theme.ninico::partials.navbar", "param_count": null, "params": [], "start": **********.875177, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/navbar.blade.phptheme.ninico::partials.navbar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fnavbar.blade.php&line=1", "ajax": false, "filename": "navbar.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.navbar"}, {"name": "1x theme.ninico::partials.header-middle", "param_count": null, "params": [], "start": **********.876854, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/header-middle.blade.phptheme.ninico::partials.header-middle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fheader-middle.blade.php&line=1", "ajax": false, "filename": "header-middle.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.header-middle"}, {"name": "1x theme.ninico::partials.mobile.menu-tab-content", "param_count": null, "params": [], "start": **********.891851, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/mobile/menu-tab-content.blade.phptheme.ninico::partials.mobile.menu-tab-content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fmobile%2Fmenu-tab-content.blade.php&line=1", "ajax": false, "filename": "menu-tab-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.mobile.menu-tab-content"}, {"name": "1x theme.ninico::partials.mobile-menu", "param_count": null, "params": [], "start": **********.911868, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/mobile-menu.blade.phptheme.ninico::partials.mobile-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fmobile-menu.blade.php&line=1", "ajax": false, "filename": "mobile-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.mobile-menu"}, {"name": "1x theme.ninico::views.ecommerce.includes.mini-cart", "param_count": null, "params": [], "start": **********.369072, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/views/ecommerce/includes/mini-cart.blade.phptheme.ninico::views.ecommerce.includes.mini-cart", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fviews%2Fecommerce%2Fincludes%2Fmini-cart.blade.php&line=1", "ajax": false, "filename": "mini-cart.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::views.ecommerce.includes.mini-cart"}, {"name": "1x theme.ninico::partials.mobile.categories-tab-content", "param_count": null, "params": [], "start": **********.371501, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/mobile/categories-tab-content.blade.phptheme.ninico::partials.mobile.categories-tab-content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fmobile%2Fcategories-tab-content.blade.php&line=1", "ajax": false, "filename": "categories-tab-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.mobile.categories-tab-content"}, {"name": "1x theme.ninico::partials.categories-dropdown-mobile", "param_count": null, "params": [], "start": **********.37209, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/categories-dropdown-mobile.blade.phptheme.ninico::partials.categories-dropdown-mobile", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fcategories-dropdown-mobile.blade.php&line=1", "ajax": false, "filename": "categories-dropdown-mobile.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.categories-dropdown-mobile"}, {"name": "1x theme.ninico::partials.footer", "param_count": null, "params": [], "start": **********.378831, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/footer.blade.phptheme.ninico::partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.footer"}, {"name": "1x theme.ninico::/../widgets/site-info/templates.frontend", "param_count": null, "params": [], "start": **********.386846, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/////widgets/site-info/templates/frontend.blade.phptheme.ninico::/../widgets/site-info/templates.frontend", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fwidgets%2Fsite-info%2Ftemplates%2Ffrontend.blade.php&line=1", "ajax": false, "filename": "frontend.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::/../widgets/site-info/templates.frontend"}, {"name": "3x theme.ninico::/../widgets/custom-menu/templates.frontend", "param_count": null, "params": [], "start": **********.391367, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/////widgets/custom-menu/templates/frontend.blade.phptheme.ninico::/../widgets/custom-menu/templates.frontend", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fwidgets%2Fcustom-menu%2Ftemplates%2Ffrontend.blade.php&line=1", "ajax": false, "filename": "frontend.blade.php", "line": "?"}, "render_count": 3, "name_original": "theme.ninico::/../widgets/custom-menu/templates.frontend"}, {"name": "3x theme.ninico::partials.footer-menu", "param_count": null, "params": [], "start": **********.400279, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/footer-menu.blade.phptheme.ninico::partials.footer-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Ffooter-menu.blade.php&line=1", "ajax": false, "filename": "footer-menu.blade.php", "line": "?"}, "render_count": 3, "name_original": "theme.ninico::partials.footer-menu"}, {"name": "1x __components::31ae887b48bc7956b164b04a31b206f3", "param_count": null, "params": [], "start": **********.503524, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/31ae887b48bc7956b164b04a31b206f3.blade.php__components::31ae887b48bc7956b164b04a31b206f3", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F31ae887b48bc7956b164b04a31b206f3.blade.php&line=1", "ajax": false, "filename": "31ae887b48bc7956b164b04a31b206f3.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::31ae887b48bc7956b164b04a31b206f3"}, {"name": "1x __components::1001eded5b0608646935941ff416e315", "param_count": null, "params": [], "start": **********.510697, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/1001eded5b0608646935941ff416e315.blade.php__components::1001eded5b0608646935941ff416e315", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F1001eded5b0608646935941ff416e315.blade.php&line=1", "ajax": false, "filename": "1001eded5b0608646935941ff416e315.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1001eded5b0608646935941ff416e315"}, {"name": "1x __components::950abe7b40c10237079259e7602acfec", "param_count": null, "params": [], "start": **********.514373, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/950abe7b40c10237079259e7602acfec.blade.php__components::950abe7b40c10237079259e7602acfec", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F950abe7b40c10237079259e7602acfec.blade.php&line=1", "ajax": false, "filename": "950abe7b40c10237079259e7602acfec.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::950abe7b40c10237079259e7602acfec"}, {"name": "1x __components::7e87b9b6cf2d20df0f49b400032abd2c", "param_count": null, "params": [], "start": **********.517493, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/7e87b9b6cf2d20df0f49b400032abd2c.blade.php__components::7e87b9b6cf2d20df0f49b400032abd2c", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F7e87b9b6cf2d20df0f49b400032abd2c.blade.php&line=1", "ajax": false, "filename": "7e87b9b6cf2d20df0f49b400032abd2c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7e87b9b6cf2d20df0f49b400032abd2c"}, {"name": "1x __components::f418e702bf973d5917d32a22532847e3", "param_count": null, "params": [], "start": **********.521498, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/f418e702bf973d5917d32a22532847e3.blade.php__components::f418e702bf973d5917d32a22532847e3", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Ff418e702bf973d5917d32a22532847e3.blade.php&line=1", "ajax": false, "filename": "f418e702bf973d5917d32a22532847e3.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::f418e702bf973d5917d32a22532847e3"}, {"name": "1x theme.ninico::/../widgets/newsletter/templates.frontend", "param_count": null, "params": [], "start": **********.537475, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/////widgets/newsletter/templates/frontend.blade.phptheme.ninico::/../widgets/newsletter/templates.frontend", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fwidgets%2Fnewsletter%2Ftemplates%2Ffrontend.blade.php&line=1", "ajax": false, "filename": "frontend.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::/../widgets/newsletter/templates.frontend"}, {"name": "2x core/js-validation::bootstrap", "param_count": null, "params": [], "start": **********.560048, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/js-validation/resources/views/bootstrap.blade.phpcore/js-validation::bootstrap", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fjs-validation%2Fresources%2Fviews%2Fbootstrap.blade.php&line=1", "ajax": false, "filename": "bootstrap.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/js-validation::bootstrap"}, {"name": "1x core/base::forms.form-content-only", "param_count": null, "params": [], "start": **********.563507, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/form-content-only.blade.phpcore/base::forms.form-content-only", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fform-content-only.blade.php&line=1", "ajax": false, "filename": "form-content-only.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.form-content-only"}, {"name": "6x core/base::forms.fields.html", "param_count": null, "params": [], "start": **********.564867, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/fields/html.blade.phpcore/base::forms.fields.html", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fhtml.blade.php&line=1", "ajax": false, "filename": "html.blade.php", "line": "?"}, "render_count": 6, "name_original": "core/base::forms.fields.html"}, {"name": "7x a74ad8dfacd4f985eb3977517615ce25::form.field", "param_count": null, "params": [], "start": **********.566741, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/form/field.blade.phpa74ad8dfacd4f985eb3977517615ce25::form.field", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 7, "name_original": "a74ad8dfacd4f985eb3977517615ce25::form.field"}, {"name": "7x core/base::forms.partials.help-block", "param_count": null, "params": [], "start": **********.56767, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/partials/help-block.blade.phpcore/base::forms.partials.help-block", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fhelp-block.blade.php&line=1", "ajax": false, "filename": "help-block.blade.php", "line": "?"}, "render_count": 7, "name_original": "core/base::forms.partials.help-block"}, {"name": "7x core/base::forms.partials.errors", "param_count": null, "params": [], "start": **********.568395, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/partials/errors.blade.phpcore/base::forms.partials.errors", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferrors.blade.php&line=1", "ajax": false, "filename": "errors.blade.php", "line": "?"}, "render_count": 7, "name_original": "core/base::forms.partials.errors"}, {"name": "8x core/base::forms.columns.column-span", "param_count": null, "params": [], "start": **********.570177, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/columns/column-span.blade.phpcore/base::forms.columns.column-span", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fcolumns%2Fcolumn-span.blade.php&line=1", "ajax": false, "filename": "column-span.blade.php", "line": "?"}, "render_count": 8, "name_original": "core/base::forms.columns.column-span"}, {"name": "1x core/base::forms.fields.email", "param_count": null, "params": [], "start": **********.572313, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/fields/email.blade.phpcore/base::forms.fields.email", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Femail.blade.php&line=1", "ajax": false, "filename": "email.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.email"}, {"name": "1x laravel-form-builder::button", "param_count": null, "params": [], "start": **********.582813, "type": "php", "hash": "phpD:\\laragon\\www\\tesmods\\platform\\packages\\form-builder\\src/../resources/views/button.phplaravel-form-builder::button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Fform-builder%2Fresources%2Fviews%2Fbutton.php&line=1", "ajax": false, "filename": "button.php", "line": "?"}, "render_count": 1, "name_original": "laravel-form-builder::button"}, {"name": "1x theme.ninico::/../widgets/cta-contact/templates.frontend", "param_count": null, "params": [], "start": **********.591525, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/////widgets/cta-contact/templates/frontend.blade.phptheme.ninico::/../widgets/cta-contact/templates.frontend", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fwidgets%2Fcta-contact%2Ftemplates%2Ffrontend.blade.php&line=1", "ajax": false, "filename": "frontend.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::/../widgets/cta-contact/templates.frontend"}, {"name": "1x theme.ninico::/../widgets/reviews-logos/templates.frontend", "param_count": null, "params": [], "start": **********.595317, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/////widgets/reviews-logos/templates/frontend.blade.phptheme.ninico::/../widgets/reviews-logos/templates.frontend", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fwidgets%2Freviews-logos%2Ftemplates%2Ffrontend.blade.php&line=1", "ajax": false, "filename": "frontend.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::/../widgets/reviews-logos/templates.frontend"}, {"name": "1x theme.ninico::layouts.base", "param_count": null, "params": [], "start": **********.597872, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/layouts/base.blade.phptheme.ninico::layouts.base", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Flayouts%2Fbase.blade.php&line=1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::layouts.base"}, {"name": "1x packages/theme::partials.header", "param_count": null, "params": [], "start": **********.623795, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/packages/theme/resources/views/partials/header.blade.phppackages/theme::partials.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::partials.header"}, {"name": "1x plugins/language::partials.hreflang", "param_count": null, "params": [], "start": **********.669778, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/plugins/language/resources/views/partials/hreflang.blade.phpplugins/language::partials.hreflang", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Flanguage%2Fresources%2Fviews%2Fpartials%2Fhreflang.blade.php&line=1", "ajax": false, "filename": "hreflang.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/language::partials.hreflang"}, {"name": "1x theme.ninico::partials.scroll-top", "param_count": null, "params": [], "start": **********.676136, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/scroll-top.blade.phptheme.ninico::partials.scroll-top", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fscroll-top.blade.php&line=1", "ajax": false, "filename": "scroll-top.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.scroll-top"}, {"name": "1x theme.ninico::partials.navigation-bar", "param_count": null, "params": [], "start": **********.685078, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/navigation-bar.blade.phptheme.ninico::partials.navigation-bar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fnavigation-bar.blade.php&line=1", "ajax": false, "filename": "navigation-bar.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.navigation-bar"}, {"name": "1x packages/theme::partials.footer", "param_count": null, "params": [], "start": **********.690741, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/packages/theme/resources/views/partials/footer.blade.phppackages/theme::partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::partials.footer"}, {"name": "1x packages/theme::admin-bar", "param_count": null, "params": [], "start": **********.703479, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/packages/theme/resources/views/admin-bar.blade.phppackages/theme::admin-bar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Fadmin-bar.blade.php&line=1", "ajax": false, "filename": "admin-bar.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::admin-bar"}, {"name": "1x packages/theme::fronts.toast-notification", "param_count": null, "params": [], "start": **********.718191, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/packages/theme/resources/views/fronts/toast-notification.blade.phppackages/theme::fronts.toast-notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Ffronts%2Ftoast-notification.blade.php&line=1", "ajax": false, "filename": "toast-notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::fronts.toast-notification"}, {"name": "4x packages/shortcode::partials.lazy-loading-script", "param_count": null, "params": [], "start": **********.721162, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/packages/shortcode/resources/views/partials/lazy-loading-script.blade.phppackages/shortcode::partials.lazy-loading-script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Fshortcode%2Fresources%2Fviews%2Fpartials%2Flazy-loading-script.blade.php&line=1", "ajax": false, "filename": "lazy-loading-script.blade.php", "line": "?"}, "render_count": 4, "name_original": "packages/shortcode::partials.lazy-loading-script"}]}, "queries": {"count": 188, "nb_statements": 188, "nb_visible_statements": 188, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.09774999999999996, "accumulated_duration_str": "97.75ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 88 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Footprints/TrackingFilter.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Services\\Footprints\\TrackingFilter.php", "line": 45}], "start": **********.852376, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 0, "width_percent": 0.491}, {"sql": "select * from `pages` where (`id` = '1' and `status` = 'published') limit 1", "type": "query", "params": [], "bindings": ["1", "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/packages/page/src/Services/PageService.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\page\\src\\Services\\PageService.php", "line": 37}, {"index": 18, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/PublicController.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php", "line": 28}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.860657, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 0.491, "width_percent": 0.532}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (1) and `slugs`.`reference_type` = 'Shaqi\\\\Page\\\\Models\\\\Page'", "type": "query", "params": [], "bindings": ["Shaqi\\Page\\Models\\Page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/page/src/Services/PageService.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\page\\src\\Services\\PageService.php", "line": 37}, {"index": 24, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/PublicController.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php", "line": 28}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.86716, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 1.023, "width_percent": 0.46}, {"sql": "select * from `ec_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 107}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/helpers/currencies.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\helpers\\currencies.php", "line": 134}, {"index": 19, "namespace": null, "name": "platform/packages/theme/src/Theme.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\theme\\src\\Theme.php", "line": 346}, {"index": 20, "namespace": null, "name": "platform/packages/theme/src/Theme.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\theme\\src\\Theme.php", "line": 895}], "start": **********.8852792, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 1.483, "width_percent": 0.481}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_id` in (1) and `meta_boxes`.`reference_type` = 'Shaqi\\\\Page\\\\Models\\\\Page'", "type": "query", "params": [], "bindings": ["Shaqi\\Page\\Models\\Page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": "view", "name": "theme.ninico::views.page", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/views/page.blade.php", "line": 2}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.8937428, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 1.964, "width_percent": 0.788}, {"sql": "select * from `simple_sliders` where `status` = 'published' and `key` = 'slider-home-1' limit 1", "type": "query", "params": [], "bindings": ["published", "slider-home-1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/simple-slider/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\simple-slider\\src\\Providers\\HookServiceProvider.php", "line": 54}, {"index": 22, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}, {"index": 23, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 101}, {"index": 24, "namespace": null, "name": "platform/packages/shortcode/src/View/View.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\shortcode\\src\\View\\View.php", "line": 57}], "start": **********.908324, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 2.752, "width_percent": 0.532}, {"sql": "select * from `simple_slider_items` where `simple_slider_items`.`simple_slider_id` = 1 and `simple_slider_items`.`simple_slider_id` is not null order by `simple_slider_items`.`order` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/simple-slider/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\simple-slider\\src\\Providers\\HookServiceProvider.php", "line": 56}, {"index": 27, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}, {"index": 28, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 101}], "start": **********.9120119, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 3.284, "width_percent": 0.419}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_id` in (1, 2, 3) and `meta_boxes`.`reference_type` = 'Shaqi\\\\SimpleSlider\\\\Models\\\\SimpleSliderItem'", "type": "query", "params": [], "bindings": ["Shaqi\\SimpleSlider\\Models\\SimpleSliderItem"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": "view", "name": "theme.ninico::partials.shortcodes.simple-slider.index", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/shortcodes/simple-slider/index.blade.php", "line": 7}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.9151502, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 3.703, "width_percent": 0.286}, {"sql": "select * from `ec_product_categories` where `status` = 'published' and `id` = '397' limit 1", "type": "query", "params": [], "bindings": ["published", "397"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/themes/ninico/functions/shortcodes.php", "file": "D:\\laragon\\www\\tesmods\\platform\\themes\\ninico\\functions\\shortcodes.php", "line": 566}, {"index": 22, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}, {"index": 23, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 101}, {"index": 24, "namespace": null, "name": "platform/packages/shortcode/src/View/View.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\shortcode\\src\\View\\View.php", "line": 57}], "start": **********.958587, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 3.99, "width_percent": 0.512}, {"sql": "select `ec_products`.*, `ec_product_category_product`.`category_id` as `pivot_category_id`, `ec_product_category_product`.`product_id` as `pivot_product_id` from `ec_products` inner join `ec_product_category_product` on `ec_products`.`id` = `ec_product_category_product`.`product_id` where `ec_product_category_product`.`category_id` = 397 and `is_variation` = 0 order by `order` asc, `created_at` asc", "type": "query", "params": [], "bindings": [397, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": null, "name": "platform/themes/ninico/functions/shortcodes.php", "file": "D:\\laragon\\www\\tesmods\\platform\\themes\\ninico\\functions\\shortcodes.php", "line": 569}, {"index": 26, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}, {"index": 27, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 101}, {"index": 28, "namespace": null, "name": "platform/packages/shortcode/src/View/View.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\shortcode\\src\\View\\View.php", "line": 57}], "start": **********.964742, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php&line=28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "tesmods", "explain": null, "start_percent": 4.501, "width_percent": 0.675}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\Product' and `slugs`.`reference_id` = 895 and `slugs`.`reference_id` is not null limit 1", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\Product", 895], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 118}, {"index": 29, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 30, "namespace": "view", "name": "theme.ninico::partials.shortcodes.maintenance-services.index", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/shortcodes/maintenance-services/index.blade.php", "line": 92}], "start": **********.969223, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 5.176, "width_percent": 0.675}, {"sql": "select * from `ec_flash_sales` where date(`end_date`) >= '2025-08-12' and `status` = 'published' order by `created_at` desc", "type": "query", "params": [], "bindings": ["2025-08-12", "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/FlashSaleRepository.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\FlashSaleRepository.php", "line": 25}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 52}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 22}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 46}], "start": **********.9868848, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 5.852, "width_percent": 0.419}, {"sql": "select * from `ec_discounts` where `type` = 'promotion' and `start_date` <= '2025-08-12 18:34:19' and (`end_date` is null or `end_date` >= '2025-08-12 18:34:19') and (`target` in ('all-orders', 'amount-minimum-order') or (`target` in ('customer', 'group-products', 'products-by-category', 'specific-product', 'product-variant') and `product_quantity` = 1))", "type": "query", "params": [], "bindings": ["promotion", "2025-08-12 18:34:19", "2025-08-12 18:34:19", "all-orders", "amount-minimum-order", "customer", "group-products", "products-by-category", "specific-product", "product-variant", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/DiscountRepository.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\DiscountRepository.php", "line": 50}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/DiscountSupport.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Supports\\DiscountSupport.php", "line": 116}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/DiscountSupport.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Supports\\DiscountSupport.php", "line": 42}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 58}], "start": **********.994633, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 6.271, "width_percent": 1.238}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\Product' and `slugs`.`reference_id` = 896 and `slugs`.`reference_id` is not null limit 1", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\Product", 896], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 118}, {"index": 29, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 30, "namespace": "view", "name": "theme.ninico::partials.shortcodes.maintenance-services.index", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/shortcodes/maintenance-services/index.blade.php", "line": 92}], "start": **********.999248, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 7.509, "width_percent": 0.379}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\Product' and `slugs`.`reference_id` = 897 and `slugs`.`reference_id` is not null limit 1", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\Product", 897], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 118}, {"index": 29, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 30, "namespace": "view", "name": "theme.ninico::partials.shortcodes.maintenance-services.index", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/shortcodes/maintenance-services/index.blade.php", "line": 92}], "start": **********.005141, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 7.887, "width_percent": 0.246}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\Product' and `slugs`.`reference_id` = 898 and `slugs`.`reference_id` is not null limit 1", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\Product", 898], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 118}, {"index": 29, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 30, "namespace": "view", "name": "theme.ninico::partials.shortcodes.maintenance-services.index", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/shortcodes/maintenance-services/index.blade.php", "line": 92}], "start": **********.011425, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 8.133, "width_percent": 0.481}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\Product' and `slugs`.`reference_id` = 899 and `slugs`.`reference_id` is not null limit 1", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\Product", 899], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 118}, {"index": 29, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 30, "namespace": "view", "name": "theme.ninico::partials.shortcodes.maintenance-services.index", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/shortcodes/maintenance-services/index.blade.php", "line": 92}], "start": **********.0227299, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 8.614, "width_percent": 0.624}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\Product' and `slugs`.`reference_id` = 900 and `slugs`.`reference_id` is not null limit 1", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\Product", 900], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 118}, {"index": 29, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 30, "namespace": "view", "name": "theme.ninico::partials.shortcodes.maintenance-services.index", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/shortcodes/maintenance-services/index.blade.php", "line": 92}], "start": **********.032031, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 9.238, "width_percent": 0.522}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\Product' and `slugs`.`reference_id` = 901 and `slugs`.`reference_id` is not null limit 1", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\Product", 901], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 118}, {"index": 29, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 30, "namespace": "view", "name": "theme.ninico::partials.shortcodes.maintenance-services.index", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/shortcodes/maintenance-services/index.blade.php", "line": 92}], "start": **********.038537, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 9.76, "width_percent": 0.409}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\Product' and `slugs`.`reference_id` = 902 and `slugs`.`reference_id` is not null limit 1", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\Product", 902], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 118}, {"index": 29, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 30, "namespace": "view", "name": "theme.ninico::partials.shortcodes.maintenance-services.index", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/shortcodes/maintenance-services/index.blade.php", "line": 92}], "start": **********.0466518, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 10.169, "width_percent": 0.44}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\Product' and `slugs`.`reference_id` = 903 and `slugs`.`reference_id` is not null limit 1", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\Product", 903], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 118}, {"index": 29, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 30, "namespace": "view", "name": "theme.ninico::partials.shortcodes.maintenance-services.index", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/shortcodes/maintenance-services/index.blade.php", "line": 92}], "start": **********.053044, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 10.609, "width_percent": 0.358}, {"sql": "select * from `announcements` where 1 = 1 and `is_active` = 1 and (`start_date` is null or `start_date` <= '2025-08-12 18:34:20') and (`end_date` is null or `end_date` >= '2025-08-12 18:34:20') order by RAND()", "type": "query", "params": [], "bindings": [1, "2025-08-12 18:34:20", "2025-08-12 18:34:20"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/announcement/src/AnnouncementHelper.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\announcement\\src\\AnnouncementHelper.php", "line": 59}, {"index": 17, "namespace": "view", "name": "theme.ninico::partials.header-top", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/header-top.blade.php", "line": 5}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.104414, "duration": 0.01324, "duration_str": "13.24ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 10.967, "width_percent": 13.545}, {"sql": "select * from `menus` where `status` = 'published' and exists (select * from `language_meta` where `menus`.`id` = `language_meta`.`reference_id` and `language_meta`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\Menu' and `lang_meta_code` = 'en_US' and `lang_meta_code` = 'en_US')", "type": "query", "params": [], "bindings": ["published", "Shaqi\\Menu\\Models\\Menu", "en_US", "en_US"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 219}, {"index": 17, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 202}, {"index": 18, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 170}, {"index": 20, "namespace": "view", "name": "theme.ninico::partials.headers.default", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/headers/default.blade.php", "line": 14}], "start": **********.135517, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 24.512, "width_percent": 0.593}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`menu_id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 219}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 202}, {"index": 24, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 170}], "start": **********.1375902, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 25.105, "width_percent": 0.859}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` in (25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 42, 44, 51, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 66, 93, 95, 128, 129, 131, 132, 133, 135, 136, 137, 138, 139, 141, 142, 143, 144, 145, 146, 147, 148, 149, 151, 152, 153, 154, 155, 156, 158, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 201, 202, 203, 204, 205, 206, 209, 210, 211, 212, 213, 214, 215, 216) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 28, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 219}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 202}], "start": **********.153511, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 25.964, "width_percent": 1.013}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_id` in (42, 44, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 66, 93, 95, 129, 131, 132, 133, 135, 136, 137, 138, 139, 141, 142, 143, 144, 145, 146, 147, 148, 149, 151, 152, 153, 154, 155, 156, 158, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 201, 202, 203, 204, 205, 206, 209, 210, 211, 212, 213, 214, 215, 216) and `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode'", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 33, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 34, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 219}], "start": **********.1659672, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 26.977, "width_percent": 0.45}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_id` in (25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 42, 44, 51, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 66, 93, 95, 128, 129, 131, 132, 133, 135, 136, 137, 138, 139, 141, 142, 143, 144, 145, 146, 147, 148, 149, 151, 152, 153, 154, 155, 156, 158, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 201, 202, 203, 204, 205, 206, 209, 210, 211, 212, 213, 214, 215, 216) and `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode'", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 28, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 219}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 202}], "start": **********.202425, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 27.427, "width_percent": 0.471}, {"sql": "select * from `menu_locations` where `menu_locations`.`menu_id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 219}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 202}, {"index": 24, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 170}], "start": **********.2212968, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 27.898, "width_percent": 0.43}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (19, 21, 213)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 24, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 180}, {"index": 26, "namespace": "view", "name": "theme.ninico::partials.headers.default", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/headers/default.blade.php", "line": 14}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.234954, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 28.327, "width_percent": 0.46}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (19, 21, 213) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 30, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 180}, {"index": 32, "namespace": "view", "name": "theme.ninico::partials.headers.default", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/headers/default.blade.php", "line": 14}], "start": **********.236717, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 28.788, "width_percent": 0.43}, {"sql": "select * from `pages` where `pages`.`id` in (12)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 24, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 180}, {"index": 26, "namespace": "view", "name": "theme.ninico::partials.headers.default", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/headers/default.blade.php", "line": 14}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.240269, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 29.217, "width_percent": 0.522}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (12) and `slugs`.`reference_type` = 'Shaqi\\\\Page\\\\Models\\\\Page'", "type": "query", "params": [], "bindings": ["Shaqi\\Page\\Models\\Page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 30, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 180}, {"index": 32, "namespace": "view", "name": "theme.ninico::partials.headers.default", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/headers/default.blade.php", "line": 14}], "start": **********.241887, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 29.739, "width_percent": 0.512}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (24, 26)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.249583, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 30.251, "width_percent": 0.481}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (24, 26) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.251147, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 30.731, "width_percent": 0.368}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 201 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [201], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.254376, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 31.1, "width_percent": 0.348}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 202 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [202], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.2611399, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 31.448, "width_percent": 0.43}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 202 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 202], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.264354, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 31.877, "width_percent": 0.43}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (41)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.267701, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 32.307, "width_percent": 0.358}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (41) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.269032, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 32.665, "width_percent": 0.246}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 209 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 209], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.273156, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 32.91, "width_percent": 0.256}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 203 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [203], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.275553, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 33.166, "width_percent": 0.552}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 203 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 203], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.279398, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 33.719, "width_percent": 0.481}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (97)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.282888, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 34.199, "width_percent": 0.512}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (97) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.284365, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 34.711, "width_percent": 0.419}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 206 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 206], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.28866, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 35.13, "width_percent": 0.317}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 204 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [204], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.2910879, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 35.448, "width_percent": 0.256}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 204 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 204], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.295104, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 35.703, "width_percent": 0.379}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (105)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.2983012, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 36.082, "width_percent": 0.46}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (105) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.2997122, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 36.542, "width_percent": 0.307}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 205 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 205], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.3043, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 36.849, "width_percent": 0.491}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (23)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.316349, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 37.34, "width_percent": 0.481}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (23) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.317838, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 37.821, "width_percent": 0.338}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 185 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [185], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.320894, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 38.159, "width_percent": 0.389}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (376)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.326359, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 38.547, "width_percent": 0.532}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (376) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.3282652, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 39.079, "width_percent": 0.45}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 165 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [165], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.331821, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 39.529, "width_percent": 0.44}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 165 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 165], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.335159, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 39.969, "width_percent": 0.338}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 186 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [186], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.339279, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 40.307, "width_percent": 0.338}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 186 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 186], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.342521, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 40.645, "width_percent": 0.501}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (34, 41)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.347458, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 41.146, "width_percent": 0.379}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (34, 41) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.348839, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 41.524, "width_percent": 0.225}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 42 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 42], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.352934, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 41.749, "width_percent": 0.256}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 53 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 53], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.356298, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 42.005, "width_percent": 0.194}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 187 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [187], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.35856, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 42.199, "width_percent": 0.286}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 187 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 187], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.362061, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 42.486, "width_percent": 0.389}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (47, 48, 49)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.36525, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 42.875, "width_percent": 0.297}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (47, 48, 49) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.366569, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 43.171, "width_percent": 0.215}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 54 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 54], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.370911, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 43.386, "width_percent": 0.225}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 56 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 56], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.374238, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 43.611, "width_percent": 0.225}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 57 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.3787498, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 43.836, "width_percent": 0.419}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 188 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [188], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.3811839, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 44.256, "width_percent": 0.266}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 188 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 188], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.383785, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 44.522, "width_percent": 0.194}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (60, 62, 65, 67)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.38673, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 44.716, "width_percent": 0.246}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (60, 62, 65, 67) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.388079, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 44.962, "width_percent": 0.205}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 60 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 60], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.393379, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 45.166, "width_percent": 0.522}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 62 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 62], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.397899, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 45.688, "width_percent": 0.419}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 63 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.401633, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 46.107, "width_percent": 0.338}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 65 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 65], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.405087, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 46.445, "width_percent": 0.256}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 189 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [189], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.4073172, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 46.701, "width_percent": 0.286}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 189 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 189], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.411952, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 46.987, "width_percent": 0.471}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (70)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.41504, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 47.458, "width_percent": 0.338}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (70) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.416152, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 47.795, "width_percent": 0.297}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 58 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.419924, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 48.092, "width_percent": 0.286}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 190 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [190], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.422017, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 48.379, "width_percent": 0.317}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 190 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 190], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.424757, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 48.696, "width_percent": 0.276}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (74, 78)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.428048, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 48.972, "width_percent": 0.44}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (74, 78) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.4295561, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 49.412, "width_percent": 0.297}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 66 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 66], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.433653, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 49.708, "width_percent": 0.307}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 64 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.436933, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 50.015, "width_percent": 0.297}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 166 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 166], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.4404428, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 50.312, "width_percent": 0.43}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 44 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [44], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.443436, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 50.742, "width_percent": 0.716}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (380, 381, 383, 382, 384, 385, 386, 387)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.4490511, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 51.458, "width_percent": 0.46}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (380, 381, 382, 383, 384, 385, 386, 387) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.450969, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 51.918, "width_percent": 0.327}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 170 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 170], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.45675, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 52.246, "width_percent": 0.399}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 169 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 169], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.463483, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 52.645, "width_percent": 0.491}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 168 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 168], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.467351, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 53.136, "width_percent": 0.409}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 167 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 167], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.4710538, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 53.545, "width_percent": 0.399}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 172 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 172], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.475541, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 53.944, "width_percent": 0.491}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 171 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 171], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.483606, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 54.435, "width_percent": 0.46}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 174 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 174], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.487732, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 54.895, "width_percent": 0.389}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.491307, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 55.284, "width_percent": 0.297}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (28, 27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.500633, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 55.581, "width_percent": 0.471}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (27, 28) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.501552, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 56.051, "width_percent": 0.307}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = ? and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5038981, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 56.358, "width_percent": 0.327}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = ? and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5082722, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 56.685, "width_percent": 0.471}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.511489, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 57.156, "width_percent": 0.44}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (145)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5139039, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 57.596, "width_percent": 0.389}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (145) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.514621, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 57.985, "width_percent": 0.368}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5181189, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 58.353, "width_percent": 0.419}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = ? and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5201519, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 58.772, "width_percent": 0.399}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.522342, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 59.171, "width_percent": 0.368}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (149)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5247529, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 59.54, "width_percent": 0.409}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (149) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.525552, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 59.949, "width_percent": 0.338}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.529943, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 60.286, "width_percent": 0.512}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = ? and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.531976, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 60.798, "width_percent": 0.399}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5342479, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 61.197, "width_percent": 0.368}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (168)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5372, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 61.565, "width_percent": 0.338}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (168) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.537875, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 61.903, "width_percent": 0.266}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5413291, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 62.169, "width_percent": 0.327}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (264, 271)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.549185, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 62.496, "width_percent": 0.46}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (264, 271) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5500681, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 62.957, "width_percent": 0.307}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = ? and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.553165, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 63.263, "width_percent": 0.481}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = ? and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5583909, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 63.744, "width_percent": 0.399}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.561359, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 64.143, "width_percent": 0.43}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (272, 273, 274)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.563988, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 64.573, "width_percent": 0.338}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (272, 273, 274) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5648592, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 64.91, "width_percent": 0.307}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.568996, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 65.217, "width_percent": 0.379}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5720181, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 65.596, "width_percent": 0.286}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5750642, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 65.882, "width_percent": 0.307}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = ? and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.577571, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 66.189, "width_percent": 0.542}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.580378, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 66.731, "width_percent": 0.45}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (275, 276, 277, 278, 279)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.583526, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 67.182, "width_percent": 0.43}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (275, 276, 277, 278, 279) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.584809, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 67.611, "width_percent": 0.348}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.589312, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 67.959, "width_percent": 0.358}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.592599, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 68.317, "width_percent": 0.399}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.596273, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 68.716, "width_percent": 0.44}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6000009, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 69.156, "width_percent": 0.389}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.603261, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 69.545, "width_percent": 0.327}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = ? and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.605135, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 69.872, "width_percent": 0.348}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.607527, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 70.22, "width_percent": 0.307}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (280, 281, 282, 284, 283, 285, 286, 287, 288)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.61295, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 70.527, "width_percent": 0.471}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (280, 281, 282, 283, 284, 285, 286, 287, 288) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.614902, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 70.997, "width_percent": 0.348}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.621727, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 71.345, "width_percent": 0.542}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.625695, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 71.887, "width_percent": 0.542}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6302922, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 72.43, "width_percent": 0.45}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6355412, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 72.88, "width_percent": 0.471}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6432278, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 73.35, "width_percent": 1.33}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.653278, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 74.68, "width_percent": 0.532}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.660258, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 75.212, "width_percent": 0.532}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.667723, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 75.744, "width_percent": 0.501}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.674933, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 76.246, "width_percent": 0.573}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = ? and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.679351, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 76.818, "width_percent": 0.675}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.68416, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 77.494, "width_percent": 0.593}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (289, 290, 291, 292, 293, 294, 295)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.688264, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 78.087, "width_percent": 0.593}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (289, 290, 291, 292, 293, 294, 295) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.691391, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 78.68, "width_percent": 0.808}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7090092, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 79.488, "width_percent": 1.013}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.717534, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 80.501, "width_percent": 0.849}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.725986, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 81.35, "width_percent": 1.238}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7346191, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 82.588, "width_percent": 0.634}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7426898, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 83.223, "width_percent": 0.573}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.747656, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 83.795, "width_percent": 0.685}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.75272, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 84.481, "width_percent": 0.45}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = ? and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.754995, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 84.931, "width_percent": 0.481}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7595758, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 85.412, "width_percent": 0.962}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (296)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.765983, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 86.373, "width_percent": 1.402}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (296) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.768143, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 87.775, "width_percent": 0.573}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7755249, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 88.348, "width_percent": 0.44}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = ? and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.779512, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 88.788, "width_percent": 0.583}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.785488, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 89.371, "width_percent": 0.552}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (297, 298, 299)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7902231, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 89.923, "width_percent": 0.522}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (297, 298, 299) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.792226, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 90.445, "width_percent": 0.512}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.800483, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 90.957, "width_percent": 0.389}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.80445, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 91.345, "width_percent": 0.45}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8080761, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 91.795, "width_percent": 0.512}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = ? and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.812443, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 92.307, "width_percent": 0.696}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (388, 390, 391, 392, 389, 393, 394, 395)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.818964, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 93.003, "width_percent": 0.634}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (388, 389, 390, 391, 392, 393, 394, 395) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.821918, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 93.637, "width_percent": 0.491}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.833945, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 94.128, "width_percent": 0.471}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.837777, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 94.598, "width_percent": 0.44}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.841426, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 95.038, "width_percent": 0.532}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8461719, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 95.57, "width_percent": 0.563}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.850872, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 96.133, "width_percent": 0.593}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.854557, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 96.726, "width_percent": 0.419}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.858055, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 97.146, "width_percent": 0.491}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8651068, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 97.637, "width_percent": 0.624}, {"sql": "select * from `widgets` where (`theme` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.381817, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 98.261, "width_percent": 0.481}, {"sql": "select * from `pages` where `pages`.`id` in (11, 8)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.393872, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 98.742, "width_percent": 0.726}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (8, 11) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.395714, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 99.468, "width_percent": 0.532}]}, "models": {"data": {"Shaqi\\Menu\\Models\\MenuNode": {"value": 280, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Fmenu%2Fsrc%2FModels%2FMenuNode.php&line=1", "ajax": false, "filename": "MenuNode.php", "line": "?"}}, "Shaqi\\Slug\\Models\\Slug": {"value": 86, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Shaqi\\Ecommerce\\Models\\ProductCategory": {"value": 74, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductCategory.php&line=1", "ajax": false, "filename": "ProductCategory.php", "line": "?"}}, "Shaqi\\Base\\Models\\MetaBox": {"value": 14, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FMetaBox.php&line=1", "ajax": false, "filename": "MetaBox.php", "line": "?"}}, "Shaqi\\Widget\\Models\\Widget": {"value": 13, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Fwidget%2Fsrc%2FModels%2FWidget.php&line=1", "ajax": false, "filename": "Widget.php", "line": "?"}}, "Shaqi\\Ecommerce\\Models\\Product": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Shaqi\\Page\\Models\\Page": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Fpage%2Fsrc%2FModels%2FPage.php&line=1", "ajax": false, "filename": "Page.php", "line": "?"}}, "Shaqi\\Menu\\Models\\Menu": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Fmenu%2Fsrc%2FModels%2FMenu.php&line=1", "ajax": false, "filename": "Menu.php", "line": "?"}}, "Shaqi\\Menu\\Models\\MenuLocation": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Fmenu%2Fsrc%2FModels%2FMenuLocation.php&line=1", "ajax": false, "filename": "MenuLocation.php", "line": "?"}}, "Shaqi\\SimpleSlider\\Models\\SimpleSliderItem": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Fsimple-slider%2Fsrc%2FModels%2FSimpleSliderItem.php&line=1", "ajax": false, "filename": "SimpleSliderItem.php", "line": "?"}}, "Shaqi\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Shaqi\\Ecommerce\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Shaqi\\SimpleSlider\\Models\\SimpleSlider": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Fsimple-slider%2Fsrc%2FModels%2FSimpleSlider.php&line=1", "ajax": false, "filename": "SimpleSlider.php", "line": "?"}}}, "count": 494, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://tesmods.gc", "action_name": "public.index", "controller_action": "Shaqi\\Theme\\Http\\Controllers\\PublicController@getIndex", "uri": "GET /", "controller": "Shaqi\\Theme\\Http\\Controllers\\PublicController@getIndex<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Ftheme%2Fsrc%2FHttp%2FControllers%2FPublicController.php&line=23\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Ftheme%2Fsrc%2FHttp%2FControllers%2FPublicController.php&line=23\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/packages/theme/src/Http/Controllers/PublicController.php:23-42</a>", "middleware": "web, core, localeSessionRedirect, localizationRedirect", "duration": "2.95s", "peak_memory": "58MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1667078869 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1667078869\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-826624209 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-826624209\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-779133802 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">tesmods.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">https://tesmods.gc/admin/ecommerce/options</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2560 characters\">shaqi_footprints_cookie=eyJpdiI6ImxHZGRqajhxQkFDcUMxb2VsdW16d1E9PSIsInZhbHVlIjoiajFBNUdGa2xjRmJPdHdMWFpTRiswdk14REJmbFh3TGp4V0JsUjZpSEhsSENiRWhSRlgwMEl3MTByNEs4UG5qanlaUEc2UlpiOFZVY0tad0g4NnRxc25hZFMvREs4K3U2NEkwcFZBQW9NK3FSdXBpZHpCYnkvUVVRU0xWMEEzMzUiLCJtYWMiOiI2OTk3Mzk3NTRlMzM2ZTdiMDExZWJmODJhZGExOTY4MmZhNmMzNjM3MDM1MWRmMTllMjY5NTUwYjBiODZjZjg1IiwidGFnIjoiIn0%3D; shaqi_footprints_cookie_data=eyJpdiI6ImF4TmlBNFJ3RTVxNFBhWUFLZkNIQWc9PSIsInZhbHVlIjoiVmxhcXR5bU4wOXU5dlhCTjVpK0JWZ2xrT3pZYXkwSllnQ3V6QklLcDRYZVpBeFhmWmJ3WGJwRTA1ZlZmcGkydGdRSHB3ZGJwdUJ3Uk9TYWYvcWtmalNoWi9mWW5SN2hzZTMvanN5cTB5TzBCaGs5NkhWVjljTFRzWWozK0pxN0p4Wjk1ckREWVhleTVjQmJZQ2ttOTlwQ1RDbHhtN3ZOMldQSUhrMExhOHFIc2VCRE9mLy83UmhCSEdkc00rckR1Y3N4NkxML1FNeGZyWitHUldZUkR0eHYyZTdBbEtMRUxqbUtGWithaGJjcVBIV2ZnaElSYllTbE96aTQ5MWNMSkNBa2wwOHM3RFQ0RW83aUpxZmVtYjU4aDhjbmo1clFSWTRHa2VzbEJLOWE3d1NoeVIvQnhrYUM1ejZUUy9qTkY0UDdsb1Q5Z3dybjVuLzZIaXRET1NadjJNSTJsbzNYd2U2aTFMOTMxYTJQejIzTU1IRFFpVm5Nd0xLdUk5Yjh2YXE1S01sRUQrMDB6OXI4U2pZeS9zQTAyaVI5TTN6bWVyV25vWGVtRWlHWkhLb1crRnVXVDJRbjJkNUY0OXM1MUUxMVpiRE9jd0wyQkg0Z0FpcDlzakRlT1NNWnVvSTI4MVNZaC9GZmhuTGNLWXBZRnNTNG9nRnY5Q3RxeTBxWUVRVjNoMDUvTkV1ajRpN096ZW4wOHlBPT0iLCJtYWMiOiJkYmI3NTYwZDUxZWJjY2I0ZmUxMmQ2YmU2YzUxYmQ3MjYzMzJlZmQ1MjMyZTBhNGNlOTUyMTgxMDhjMzhlNjU1IiwidGFnIjoiIn0%3D; cookie_for_consent=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlVHb3RYN0M5L2FRa3ovaExFNWVXQ3c9PSIsInZhbHVlIjoiMFU4V0hvaGRDVXI5K3RzdzdkMjZ1eDZ3cnhXQy9nRUpxekg1bFIxa0VoUTBTY2x6TTB4SlBGL1lkbVpOV2IyRitMcGsyMHFOS2ZMalRDQWZzM1g0MThCcWpheHNMSE5QT2g4ZUhhNFRxTnNUL0ZuKzhyNnpUbXI1V3JMSEhlT0laT2hXN0JUWWd4MEhXMXBhTzhaZkh2VkR4TXF4NXlDNnpUbzcxMjdnL1RwUzduZ05BMy9iTnZDNTlEdlBWaW5RNDkraERQRXI3RmgzNzEzclFrTkQ5S21kRmNWK1I0OVhGUGx2cXpzK0tJdz0iLCJtYWMiOiJhZDg0MDlkNzAxNWI5MjBiN2NlZmE5ZDRkNDExNDg3MjdiMTY1MWY0NDdjNjZkNmI5ZDA1ZTE0MWIwNzc4M2E1IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlJFSnpnbkFqSmdOcTZFZFJsRXhHYmc9PSIsInZhbHVlIjoiRlErN0pGQ20vUG9OMzFkZUZQWkRPN2NQVW9WNE1qUjA5VDduYnp3TTdPRnNacDl2Qlg2TUNMdUlaOG8vTkNuTVJzMGNMQ2x0bGVOMHR3bFZ5SVQyVmhpTUpWWEV3QUtvc3BSWmtSdU96S2R0MU1kV1dPbGhuYVNRQWp1Q1ArS3YiLCJtYWMiOiIyNzgxN2M1YzUxOWYwMWU2MjMyMzRkMzA3NDdiZjMxYmQ4ZGJkOTBhNzY1ZWRhZjEyMTlhN2ZlMDcxMTI0NjY4IiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6Ijkva2xNbmpJWHhwSGZPWmpwQnorWEE9PSIsInZhbHVlIjoibkRFdUlYTmhkOEFTeTczQTVEall4QkVleFp5S0dkNzcrd2NkMGFUTGptMFRRdWFDZVltWm02MS9SYTY4akhGUFdRUnVGMk9KRWV1d25scVVsQlFGTnBMcmNOTUFUczNCdTREYVZhMW1zUWxxaEM1UVR3bzFUdmdaNGxPUWxJajMiLCJtYWMiOiI1Nzc5YTg2M2NjZTUzNmU0NzM5ZDc0YWQ4NmUzZjM2MzUzNzZiZDQ2N2NiZjI5NjkxMmQ5MTViN2E3ZWVjMmEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-779133802\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-209740445 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>shaqi_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7a4e1a8bf2884ebcac0020a875b7168cc919bd88</span>\"\n  \"<span class=sf-dump-key>shaqi_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"346 characters\">{&quot;footprint&quot;:&quot;7a4e1a8bf2884ebcac0020a875b7168cc919bd88&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;tesmods.gc&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|IhQd5rv2OiHN31FogUE4N7nywrkbjngoKJ2fGSpGDJTc8ESMBnaVyTbMe1uM|$2y$12$Y0fKdUtNl.7uriE2jyqMteM5YHmP0ovxpe3RbMWYUS/IwySBzdTYK</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">21kNLtyrrz1EnHCRTflWSzE24GnCtMPJmB7j18C5</span>\"\n  \"<span class=sf-dump-key>shaqi_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">259NiGAyxSp79a8W1RViospfuj3oZ9d3ieXItl0L</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-209740445\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 18:34:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cms-version</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">7.5.2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization-at</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>activated-license</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">No</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-918729704 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">21kNLtyrrz1EnHCRTflWSzE24GnCtMPJmB7j18C5</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">https://tesmods.gc/admin/ecommerce/options</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-918729704\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://tesmods.gc", "action_name": "public.index", "controller_action": "Shaqi\\Theme\\Http\\Controllers\\PublicController@getIndex"}, "badge": null}}