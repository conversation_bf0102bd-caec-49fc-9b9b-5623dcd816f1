{"__meta": {"id": "01K2FPEXK6TR9EA35FYFYDFRFR", "datetime": "2025-08-12 17:50:14", "utime": **********.630982, "method": "GET", "uri": "/admin/ecommerce/options/create", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.139732, "end": **********.630995, "duration": 1.4912631511688232, "duration_str": "1.49s", "measures": [{"label": "Booting", "start": **********.139732, "relative_start": 0, "end": **********.931358, "relative_end": **********.931358, "duration": 0.****************, "duration_str": "792ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.931372, "relative_start": 0.***************, "end": **********.630997, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "700ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.949887, "relative_start": 0.****************, "end": **********.957187, "relative_end": **********.957187, "duration": 0.007299900054931641, "duration_str": "7.3ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: plugins/ecommerce::product-options.option-admin", "start": **********.997919, "relative_start": 0.***************, "end": **********.997919, "relative_end": **********.997919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.header.cell", "start": **********.001898, "relative_start": 0.***************, "end": **********.001898, "relative_end": **********.001898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.header.cell", "start": **********.002245, "relative_start": 0.8625130653381348, "end": **********.002245, "relative_end": **********.002245, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.header.cell", "start": **********.002438, "relative_start": 0.862706184387207, "end": **********.002438, "relative_end": **********.002438, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.header.index", "start": **********.002643, "relative_start": 0.8629112243652344, "end": **********.002643, "relative_end": **********.002643, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d634f8cc23c050fb53f76b965496cac0", "start": **********.005333, "relative_start": 0.8656010627746582, "end": **********.005333, "relative_end": **********.005333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.body.cell", "start": **********.005658, "relative_start": 0.8659260272979736, "end": **********.005658, "relative_end": **********.005658, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.body.cell", "start": **********.005968, "relative_start": 0.8662362098693848, "end": **********.005968, "relative_end": **********.005968, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.body.cell", "start": **********.006161, "relative_start": 0.8664290904998779, "end": **********.006161, "relative_end": **********.006161, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.body.cell", "start": **********.006355, "relative_start": 0.8666231632232666, "end": **********.006355, "relative_end": **********.006355, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.006641, "relative_start": 0.8669090270996094, "end": **********.006641, "relative_end": **********.006641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e38e2f7375f7e06238efe5faa11bb3f5", "start": **********.00787, "relative_start": 0.8681380748748779, "end": **********.00787, "relative_end": **********.00787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.body.cell", "start": **********.008128, "relative_start": 0.8683960437774658, "end": **********.008128, "relative_end": **********.008128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.body.row", "start": **********.0084, "relative_start": 0.8686680793762207, "end": **********.0084, "relative_end": **********.0084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.body.index", "start": **********.008772, "relative_start": 0.8690400123596191, "end": **********.008772, "relative_end": **********.008772, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.index", "start": **********.009281, "relative_start": 0.869549036026001, "end": **********.009281, "relative_end": **********.009281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.00988, "relative_start": 0.8701481819152832, "end": **********.00988, "relative_end": **********.00988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.form", "start": **********.013795, "relative_start": 0.8740630149841309, "end": **********.013795, "relative_end": **********.013795, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.017895, "relative_start": 0.8781630992889404, "end": **********.017895, "relative_end": **********.017895, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.018584, "relative_start": 0.878852128982544, "end": **********.018584, "relative_end": **********.018584, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.019138, "relative_start": 0.879406213760376, "end": **********.019138, "relative_end": **********.019138, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.020497, "relative_start": 0.8807651996612549, "end": **********.020497, "relative_end": **********.020497, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.021152, "relative_start": 0.8814201354980469, "end": **********.021152, "relative_end": **********.021152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.021552, "relative_start": 0.8818202018737793, "end": **********.021552, "relative_end": **********.021552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.022067, "relative_start": 0.8823351860046387, "end": **********.022067, "relative_end": **********.022067, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.body.index", "start": **********.022791, "relative_start": 0.883059024810791, "end": **********.022791, "relative_end": **********.022791, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.023133, "relative_start": 0.8834011554718018, "end": **********.023133, "relative_end": **********.023133, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.meta-box", "start": **********.023621, "relative_start": 0.8838891983032227, "end": **********.023621, "relative_end": **********.023621, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": **********.024706, "relative_start": 0.8849740028381348, "end": **********.024706, "relative_end": **********.024706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.025153, "relative_start": 0.8854210376739502, "end": **********.025153, "relative_end": **********.025153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.body.index", "start": **********.025457, "relative_start": 0.8857250213623047, "end": **********.025457, "relative_end": **********.025457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.025668, "relative_start": 0.8859360218048096, "end": **********.025668, "relative_end": **********.025668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.meta-box", "start": **********.028051, "relative_start": 0.8883190155029297, "end": **********.028051, "relative_end": **********.028051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.form-actions", "start": **********.028554, "relative_start": 0.888822078704834, "end": **********.028554, "relative_end": **********.028554, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": **********.029613, "relative_start": 0.8898811340332031, "end": **********.029613, "relative_end": **********.029613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.030056, "relative_start": 0.8903241157531738, "end": **********.030056, "relative_end": **********.030056, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.form-buttons", "start": **********.030476, "relative_start": 0.8907442092895508, "end": **********.030476, "relative_end": **********.030476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.031203, "relative_start": 0.8914711475372314, "end": **********.031203, "relative_end": **********.031203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d25e365b684c7fa9ad5ec13cfb768fc1", "start": **********.032382, "relative_start": 0.8926501274108887, "end": **********.032382, "relative_end": **********.032382, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.032679, "relative_start": 0.8929471969604492, "end": **********.032679, "relative_end": **********.032679, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5db12cde11beb11dd9ef0499874ec197", "start": **********.033739, "relative_start": 0.8940072059631348, "end": **********.033739, "relative_end": **********.033739, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.body.index", "start": **********.034266, "relative_start": 0.8945341110229492, "end": **********.034266, "relative_end": **********.034266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.034487, "relative_start": 0.8947551250457764, "end": **********.034487, "relative_end": **********.034487, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.breadcrumbs", "start": **********.03746, "relative_start": 0.8977282047271729, "end": **********.03746, "relative_end": **********.03746, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.form-buttons", "start": **********.037861, "relative_start": 0.8981292247772217, "end": **********.037861, "relative_end": **********.037861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.038431, "relative_start": 0.8986990451812744, "end": **********.038431, "relative_end": **********.038431, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d25e365b684c7fa9ad5ec13cfb768fc1", "start": **********.039159, "relative_start": 0.8994271755218506, "end": **********.039159, "relative_end": **********.039159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.039418, "relative_start": 0.8996860980987549, "end": **********.039418, "relative_end": **********.039418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5db12cde11beb11dd9ef0499874ec197", "start": **********.040104, "relative_start": 0.9003720283508301, "end": **********.040104, "relative_end": **********.040104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.meta-box", "start": **********.041485, "relative_start": 0.9017531871795654, "end": **********.041485, "relative_end": **********.041485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.041792, "relative_start": 0.9020600318908691, "end": **********.041792, "relative_end": **********.041792, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.042144, "relative_start": 0.9024121761322021, "end": **********.042144, "relative_end": **********.042144, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": **********.042491, "relative_start": 0.9027590751647949, "end": **********.042491, "relative_end": **********.042491, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.0432, "relative_start": 0.903468132019043, "end": **********.0432, "relative_end": **********.0432, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-select", "start": **********.044878, "relative_start": 0.9051461219787598, "end": **********.044878, "relative_end": **********.044878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": **********.046025, "relative_start": 0.9062931537628174, "end": **********.046025, "relative_end": **********.046025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.046738, "relative_start": 0.907006025314331, "end": **********.046738, "relative_end": **********.046738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.047456, "relative_start": 0.907724142074585, "end": **********.047456, "relative_end": **********.047456, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.047819, "relative_start": 0.9080870151519775, "end": **********.047819, "relative_end": **********.047819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.048233, "relative_start": 0.908501148223877, "end": **********.048233, "relative_end": **********.048233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.048651, "relative_start": 0.908919095993042, "end": **********.048651, "relative_end": **********.048651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.049207, "relative_start": 0.9094750881195068, "end": **********.049207, "relative_end": **********.049207, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.049793, "relative_start": 0.9100611209869385, "end": **********.049793, "relative_end": **********.049793, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": **********.050319, "relative_start": 0.9105870723724365, "end": **********.050319, "relative_end": **********.050319, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.050793, "relative_start": 0.9110610485076904, "end": **********.050793, "relative_end": **********.050793, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.on-off", "start": **********.051427, "relative_start": 0.9116950035095215, "end": **********.051427, "relative_end": **********.051427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.on-off", "start": **********.052391, "relative_start": 0.9126591682434082, "end": **********.052391, "relative_end": **********.052391, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.toggle", "start": **********.053223, "relative_start": 0.9134910106658936, "end": **********.053223, "relative_end": **********.053223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.053972, "relative_start": 0.9142401218414307, "end": **********.053972, "relative_end": **********.053972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.054672, "relative_start": 0.9149401187896729, "end": **********.054672, "relative_end": **********.054672, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.055109, "relative_start": 0.915377140045166, "end": **********.055109, "relative_end": **********.055109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.055854, "relative_start": 0.9161221981048584, "end": **********.055854, "relative_end": **********.055854, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.056425, "relative_start": 0.9166932106018066, "end": **********.056425, "relative_end": **********.056425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.meta-box", "start": **********.058586, "relative_start": 0.9188539981842041, "end": **********.058586, "relative_end": **********.058586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.0857, "relative_start": 0.9459681510925293, "end": **********.0857, "relative_end": **********.0857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.master", "start": **********.087, "relative_start": 0.947268009185791, "end": **********.087, "relative_end": **********.087, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.before-content", "start": **********.088405, "relative_start": 0.9486730098724365, "end": **********.088405, "relative_end": **********.088405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.header", "start": **********.089108, "relative_start": 0.949376106262207, "end": **********.089108, "relative_end": **********.089108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::133aa97c11fca0f84f02ebcb9fd067dc", "start": **********.091597, "relative_start": 0.9518651962280273, "end": **********.091597, "relative_end": **********.091597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.logo", "start": **********.092302, "relative_start": 0.9525701999664307, "end": **********.092302, "relative_end": **********.092302, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::global-search.navbar-input", "start": **********.107325, "relative_start": 0.9675931930541992, "end": **********.107325, "relative_end": **********.107325, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.text-input", "start": **********.107974, "relative_start": 0.9682421684265137, "end": **********.107974, "relative_end": **********.107974, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.109327, "relative_start": 0.969595193862915, "end": **********.109327, "relative_end": **********.109327, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.error", "start": **********.110281, "relative_start": 0.9705491065979004, "end": **********.110281, "relative_end": **********.110281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": **********.111495, "relative_start": 0.9717631340026855, "end": **********.111495, "relative_end": **********.111495, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.113173, "relative_start": 0.9734411239624023, "end": **********.113173, "relative_end": **********.113173, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dc988bb05638c97d86c3bc8a9b727e31", "start": **********.114439, "relative_start": 0.9747071266174316, "end": **********.114439, "relative_end": **********.114439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.theme-toggle", "start": **********.114845, "relative_start": 0.9751131534576416, "end": **********.114845, "relative_end": **********.114845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ec711b4f47c4b42ebc81a7564c1d8d33", "start": **********.116396, "relative_start": 0.9766640663146973, "end": **********.116396, "relative_end": **********.116396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::notification.nav-item", "start": **********.118379, "relative_start": 0.9786472320556641, "end": **********.118379, "relative_end": **********.118379, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::306e03d3dc5634ee2e82192f553c6f9b", "start": **********.119871, "relative_start": 0.9801390171051025, "end": **********.119871, "relative_end": **********.119871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/contact::partials.notification", "start": **********.123446, "relative_start": 0.9837141036987305, "end": **********.123446, "relative_end": **********.123446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "start": **********.124953, "relative_start": 0.9852211475372314, "end": **********.124953, "relative_end": **********.124953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": **********.126132, "relative_start": 0.9864001274108887, "end": **********.126132, "relative_end": **********.126132, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.actions", "start": **********.127057, "relative_start": 0.9873251914978027, "end": **********.127057, "relative_end": **********.127057, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.127529, "relative_start": 0.9877970218658447, "end": **********.127529, "relative_end": **********.127529, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.166234, "relative_start": 1.0265021324157715, "end": **********.166234, "relative_end": **********.166234, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.user-menu", "start": **********.170439, "relative_start": 1.0307071208953857, "end": **********.170439, "relative_end": **********.170439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.item", "start": **********.178095, "relative_start": 1.0383632183074951, "end": **********.178095, "relative_end": **********.178095, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2907df26a6102c24ab0c37391217b338", "start": **********.179503, "relative_start": 1.0397710800170898, "end": **********.179503, "relative_end": **********.179503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.item", "start": **********.179913, "relative_start": 1.0401811599731445, "end": **********.179913, "relative_end": **********.179913, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f38bffca7b8a1a50e97a6950ffd66c5c", "start": **********.181356, "relative_start": 1.0416240692138672, "end": **********.181356, "relative_end": **********.181356, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.index", "start": **********.181838, "relative_start": 1.0421061515808105, "end": **********.181838, "relative_end": **********.181838, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.aside", "start": **********.183019, "relative_start": 1.0432870388031006, "end": **********.183019, "relative_end": **********.183019, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::133aa97c11fca0f84f02ebcb9fd067dc", "start": **********.184502, "relative_start": 1.0447700023651123, "end": **********.184502, "relative_end": **********.184502, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.logo", "start": **********.184829, "relative_start": 1.0450971126556396, "end": **********.184829, "relative_end": **********.184829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.item", "start": **********.187783, "relative_start": 1.048051118850708, "end": **********.187783, "relative_end": **********.187783, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2907df26a6102c24ab0c37391217b338", "start": **********.188803, "relative_start": 1.0490710735321045, "end": **********.188803, "relative_end": **********.188803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.item", "start": **********.189252, "relative_start": 1.0495200157165527, "end": **********.189252, "relative_end": **********.189252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f38bffca7b8a1a50e97a6950ffd66c5c", "start": **********.190058, "relative_start": 1.0503261089324951, "end": **********.190058, "relative_end": **********.190058, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.index", "start": **********.190388, "relative_start": 1.0506560802459717, "end": **********.190388, "relative_end": **********.190388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.sidebar", "start": **********.191297, "relative_start": 1.051565170288086, "end": **********.191297, "relative_end": **********.191297, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav", "start": **********.191836, "relative_start": 1.0521042346954346, "end": **********.191836, "relative_end": **********.191836, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.208886, "relative_start": 1.0691540241241455, "end": **********.208886, "relative_end": **********.208886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.210164, "relative_start": 1.070432186126709, "end": **********.210164, "relative_end": **********.210164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::af68cda57c5ca67f3b8a7729953880bc", "start": **********.212913, "relative_start": 1.07318115234375, "end": **********.212913, "relative_end": **********.212913, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.213926, "relative_start": 1.0741941928863525, "end": **********.213926, "relative_end": **********.213926, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.215095, "relative_start": 1.0753631591796875, "end": **********.215095, "relative_end": **********.215095, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c50817c59f218fea1b6c19be4db347dd", "start": **********.216979, "relative_start": 1.077247142791748, "end": **********.216979, "relative_end": **********.216979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.217772, "relative_start": 1.0780401229858398, "end": **********.217772, "relative_end": **********.217772, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.218381, "relative_start": 1.0786490440368652, "end": **********.218381, "relative_end": **********.218381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.218955, "relative_start": 1.0792231559753418, "end": **********.218955, "relative_end": **********.218955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::42d71f4fe12493bd6829a606380e1435", "start": **********.220965, "relative_start": 1.081233024597168, "end": **********.220965, "relative_end": **********.220965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.221571, "relative_start": 1.0818390846252441, "end": **********.221571, "relative_end": **********.221571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1a65d1071503a2e05e73a706bfebac58", "start": **********.224909, "relative_start": 1.0851771831512451, "end": **********.224909, "relative_end": **********.224909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.225525, "relative_start": 1.0857930183410645, "end": **********.225525, "relative_end": **********.225525, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.22587, "relative_start": 1.0861380100250244, "end": **********.22587, "relative_end": **********.22587, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.22639, "relative_start": 1.086658000946045, "end": **********.22639, "relative_end": **********.22639, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7e33c8bf87ccc47b22832a4eb415ca9d", "start": **********.227964, "relative_start": 1.0882320404052734, "end": **********.227964, "relative_end": **********.227964, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.228924, "relative_start": 1.0891921520233154, "end": **********.228924, "relative_end": **********.228924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::452ccf81d5bbacec7ec062999540e293", "start": **********.23074, "relative_start": 1.091008186340332, "end": **********.23074, "relative_end": **********.23074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.231381, "relative_start": 1.091649055480957, "end": **********.231381, "relative_end": **********.231381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.231746, "relative_start": 1.0920140743255615, "end": **********.231746, "relative_end": **********.231746, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.232227, "relative_start": 1.0924952030181885, "end": **********.232227, "relative_end": **********.232227, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ee52ba22bc1262334c4146935fbed227", "start": **********.23461, "relative_start": 1.0948781967163086, "end": **********.23461, "relative_end": **********.23461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.23522, "relative_start": 1.0954880714416504, "end": **********.23522, "relative_end": **********.23522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7bd0601fe0dc4645b269458a9ed5f144", "start": **********.23722, "relative_start": 1.0974881649017334, "end": **********.23722, "relative_end": **********.23722, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.23789, "relative_start": 1.0981581211090088, "end": **********.23789, "relative_end": **********.23789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5e1ed162565cf31bd543a8427caaef1e", "start": **********.239396, "relative_start": 1.0996642112731934, "end": **********.239396, "relative_end": **********.239396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.240022, "relative_start": 1.100290060043335, "end": **********.240022, "relative_end": **********.240022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e3de92deb18074b63f585befac7d0965", "start": **********.24192, "relative_start": 1.1021881103515625, "end": **********.24192, "relative_end": **********.24192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.242864, "relative_start": 1.1031320095062256, "end": **********.242864, "relative_end": **********.242864, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::69ab4d8cea5c292eefba375a85e9768a", "start": **********.245582, "relative_start": 1.1058502197265625, "end": **********.245582, "relative_end": **********.245582, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.246203, "relative_start": 1.106471061706543, "end": **********.246203, "relative_end": **********.246203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::64f291ea9b97f679266b5e47f5ae8464", "start": **********.247725, "relative_start": 1.1079931259155273, "end": **********.247725, "relative_end": **********.247725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.248308, "relative_start": 1.1085760593414307, "end": **********.248308, "relative_end": **********.248308, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9413e731e9bb6e320e018067130903e9", "start": **********.249816, "relative_start": 1.110084056854248, "end": **********.249816, "relative_end": **********.249816, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.250464, "relative_start": 1.110732078552246, "end": **********.250464, "relative_end": **********.250464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::59b84d05fdb5c77612cfb696a101e213", "start": **********.252634, "relative_start": 1.1129021644592285, "end": **********.252634, "relative_end": **********.252634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.253294, "relative_start": 1.1135621070861816, "end": **********.253294, "relative_end": **********.253294, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2c23a08a8a248b19ed43094ec82cfb6b", "start": **********.255016, "relative_start": 1.1152842044830322, "end": **********.255016, "relative_end": **********.255016, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.255682, "relative_start": 1.115950107574463, "end": **********.255682, "relative_end": **********.255682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::59b84d05fdb5c77612cfb696a101e213", "start": **********.257254, "relative_start": 1.1175220012664795, "end": **********.257254, "relative_end": **********.257254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.257879, "relative_start": 1.1181471347808838, "end": **********.257879, "relative_end": **********.257879, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1859ae2c66dc0538fd83aadc7f316844", "start": **********.262108, "relative_start": 1.1223762035369873, "end": **********.262108, "relative_end": **********.262108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.262852, "relative_start": 1.1231200695037842, "end": **********.262852, "relative_end": **********.262852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ccda1407f97c36756562e2c01f09a7ab", "start": **********.264563, "relative_start": 1.124831199645996, "end": **********.264563, "relative_end": **********.264563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.265224, "relative_start": 1.1254920959472656, "end": **********.265224, "relative_end": **********.265224, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::52309442cd989852b03aa65d96b55790", "start": **********.267443, "relative_start": 1.1277110576629639, "end": **********.267443, "relative_end": **********.267443, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.269873, "relative_start": 1.130141019821167, "end": **********.269873, "relative_end": **********.269873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2cd2bd3a1a36cafc00007578f96771de", "start": **********.271628, "relative_start": 1.1318960189819336, "end": **********.271628, "relative_end": **********.271628, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.272148, "relative_start": 1.132416009902954, "end": **********.272148, "relative_end": **********.272148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::24c50807435b9d6d46a8d65cd016c8b0", "start": **********.273921, "relative_start": 1.1341891288757324, "end": **********.273921, "relative_end": **********.273921, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.274426, "relative_start": 1.1346940994262695, "end": **********.274426, "relative_end": **********.274426, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::13365b7e5a448d13150fdb4b3884b510", "start": **********.276038, "relative_start": 1.1363060474395752, "end": **********.276038, "relative_end": **********.276038, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.276999, "relative_start": 1.1372671127319336, "end": **********.276999, "relative_end": **********.276999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.277844, "relative_start": 1.1381120681762695, "end": **********.277844, "relative_end": **********.277844, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dd4c2087b0a47210b5b4e3ee87ef3eca", "start": **********.279352, "relative_start": 1.139620065689087, "end": **********.279352, "relative_end": **********.279352, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.279894, "relative_start": 1.1401622295379639, "end": **********.279894, "relative_end": **********.279894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.280533, "relative_start": 1.140801191329956, "end": **********.280533, "relative_end": **********.280533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::998e4178ecae37dacf7321232f455f64", "start": **********.281837, "relative_start": 1.1421051025390625, "end": **********.281837, "relative_end": **********.281837, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.282365, "relative_start": 1.1426331996917725, "end": **********.282365, "relative_end": **********.282365, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06e7cfabd119917d6efbd595a344e37b", "start": **********.284054, "relative_start": 1.144322156906128, "end": **********.284054, "relative_end": **********.284054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.284583, "relative_start": 1.1448512077331543, "end": **********.284583, "relative_end": **********.284583, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6e0b6ed9bf49c6ad9d02af2c7f911103", "start": **********.286271, "relative_start": 1.1465392112731934, "end": **********.286271, "relative_end": **********.286271, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.286799, "relative_start": 1.1470670700073242, "end": **********.286799, "relative_end": **********.286799, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9413e731e9bb6e320e018067130903e9", "start": **********.28798, "relative_start": 1.1482481956481934, "end": **********.28798, "relative_end": **********.28798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.288477, "relative_start": 1.148745059967041, "end": **********.288477, "relative_end": **********.288477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.289164, "relative_start": 1.1494321823120117, "end": **********.289164, "relative_end": **********.289164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ca20cd1247722214b06db9aa7c493b27", "start": **********.291394, "relative_start": 1.1516621112823486, "end": **********.291394, "relative_end": **********.291394, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.292293, "relative_start": 1.1525611877441406, "end": **********.292293, "relative_end": **********.292293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.292688, "relative_start": 1.1529560089111328, "end": **********.292688, "relative_end": **********.292688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.293268, "relative_start": 1.153536081314087, "end": **********.293268, "relative_end": **********.293268, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.294694, "relative_start": 1.1549620628356934, "end": **********.294694, "relative_end": **********.294694, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.29518, "relative_start": 1.1554481983184814, "end": **********.29518, "relative_end": **********.29518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.295472, "relative_start": 1.1557400226593018, "end": **********.295472, "relative_end": **********.295472, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.295874, "relative_start": 1.156142234802246, "end": **********.295874, "relative_end": **********.295874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.296627, "relative_start": 1.1568951606750488, "end": **********.296627, "relative_end": **********.296627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.297079, "relative_start": 1.1573472023010254, "end": **********.297079, "relative_end": **********.297079, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.297785, "relative_start": 1.1580531597137451, "end": **********.297785, "relative_end": **********.297785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.298256, "relative_start": 1.1585240364074707, "end": **********.298256, "relative_end": **********.298256, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.298904, "relative_start": 1.1591720581054688, "end": **********.298904, "relative_end": **********.298904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::df3c1e6b28f622836bfe7885f46c5f8b", "start": **********.300362, "relative_start": 1.160630226135254, "end": **********.300362, "relative_end": **********.300362, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.300933, "relative_start": 1.161201000213623, "end": **********.300933, "relative_end": **********.300933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.301595, "relative_start": 1.161863088607788, "end": **********.301595, "relative_end": **********.301595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::13365b7e5a448d13150fdb4b3884b510", "start": **********.302435, "relative_start": 1.162703037261963, "end": **********.302435, "relative_end": **********.302435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.302906, "relative_start": 1.1631741523742676, "end": **********.302906, "relative_end": **********.302906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.303521, "relative_start": 1.1637890338897705, "end": **********.303521, "relative_end": **********.303521, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a3acd3bd206d0793e18d491720de886c", "start": **********.305333, "relative_start": 1.1656010150909424, "end": **********.305333, "relative_end": **********.305333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.305872, "relative_start": 1.166140079498291, "end": **********.305872, "relative_end": **********.305872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.306503, "relative_start": 1.1667711734771729, "end": **********.306503, "relative_end": **********.306503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fd978891e1ac33723cbffddc6658659a", "start": **********.308462, "relative_start": 1.1687300205230713, "end": **********.308462, "relative_end": **********.308462, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.309038, "relative_start": 1.1693060398101807, "end": **********.309038, "relative_end": **********.309038, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.309891, "relative_start": 1.170159101486206, "end": **********.309891, "relative_end": **********.309891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.310529, "relative_start": 1.1707971096038818, "end": **********.310529, "relative_end": **********.310529, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.311489, "relative_start": 1.1717572212219238, "end": **********.311489, "relative_end": **********.311489, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.312003, "relative_start": 1.1722710132598877, "end": **********.312003, "relative_end": **********.312003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.312643, "relative_start": 1.1729111671447754, "end": **********.312643, "relative_end": **********.312643, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::95c79b13976f7182ec015ce149dd5974", "start": **********.314626, "relative_start": 1.174894094467163, "end": **********.314626, "relative_end": **********.314626, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.315161, "relative_start": 1.175429105758667, "end": **********.315161, "relative_end": **********.315161, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.3158, "relative_start": 1.1760680675506592, "end": **********.3158, "relative_end": **********.3158, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "start": **********.316586, "relative_start": 1.176854133605957, "end": **********.316586, "relative_end": **********.316586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.317028, "relative_start": 1.1772961616516113, "end": **********.317028, "relative_end": **********.317028, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.31733, "relative_start": 1.177597999572754, "end": **********.31733, "relative_end": **********.31733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.317748, "relative_start": 1.178016185760498, "end": **********.317748, "relative_end": **********.317748, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cbce7a4c13e13a70eabb7759894a60fd", "start": **********.319024, "relative_start": 1.1792922019958496, "end": **********.319024, "relative_end": **********.319024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.319523, "relative_start": 1.1797912120819092, "end": **********.319523, "relative_end": **********.319523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fe6fcb7551f99a7d9d1c5a4c0011f471", "start": **********.320766, "relative_start": 1.1810340881347656, "end": **********.320766, "relative_end": **********.320766, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.321272, "relative_start": 1.1815400123596191, "end": **********.321272, "relative_end": **********.321272, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.321892, "relative_start": 1.1821601390838623, "end": **********.321892, "relative_end": **********.321892, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e81d33e262b34e339fefa952b8ffa5f1", "start": **********.32332, "relative_start": 1.1835880279541016, "end": **********.32332, "relative_end": **********.32332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.32388, "relative_start": 1.1841480731964111, "end": **********.32388, "relative_end": **********.32388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.324527, "relative_start": 1.1847951412200928, "end": **********.324527, "relative_end": **********.324527, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::351bdfbe842eff22e08f1df9d5f5beb1", "start": **********.326462, "relative_start": 1.186730146408081, "end": **********.326462, "relative_end": **********.326462, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.327055, "relative_start": 1.1873230934143066, "end": **********.327055, "relative_end": **********.327055, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fda51db955ba828a198ee1c8d52b2003", "start": **********.328428, "relative_start": 1.1886961460113525, "end": **********.328428, "relative_end": **********.328428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.328951, "relative_start": 1.1892189979553223, "end": **********.328951, "relative_end": **********.328951, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6e0b6ed9bf49c6ad9d02af2c7f911103", "start": **********.330048, "relative_start": 1.1903162002563477, "end": **********.330048, "relative_end": **********.330048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.330522, "relative_start": 1.1907901763916016, "end": **********.330522, "relative_end": **********.330522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.33119, "relative_start": 1.1914582252502441, "end": **********.33119, "relative_end": **********.33119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "start": **********.332552, "relative_start": 1.1928200721740723, "end": **********.332552, "relative_end": **********.332552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.333043, "relative_start": 1.1933112144470215, "end": **********.333043, "relative_end": **********.333043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.333676, "relative_start": 1.1939442157745361, "end": **********.333676, "relative_end": **********.333676, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0343a1b0800146d7d9cf6a9514ec7bf4", "start": **********.335275, "relative_start": 1.1955430507659912, "end": **********.335275, "relative_end": **********.335275, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.335813, "relative_start": 1.1960811614990234, "end": **********.335813, "relative_end": **********.335813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::15422be7d0f2aaf8c8244c1e9db20ad9", "start": **********.337421, "relative_start": 1.1976890563964844, "end": **********.337421, "relative_end": **********.337421, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.337962, "relative_start": 1.1982300281524658, "end": **********.337962, "relative_end": **********.337962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2e5e965add6d0ee3aadb02ca38e70825", "start": **********.33968, "relative_start": 1.1999480724334717, "end": **********.33968, "relative_end": **********.33968, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.340211, "relative_start": 1.2004790306091309, "end": **********.340211, "relative_end": **********.340211, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5b8d99843e0f8eff6046d7236026b187", "start": **********.341873, "relative_start": 1.2021410465240479, "end": **********.341873, "relative_end": **********.341873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.342395, "relative_start": 1.2026631832122803, "end": **********.342395, "relative_end": **********.342395, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d6f928aaf1e585d3246cb3bee8fdd45", "start": **********.344452, "relative_start": 1.2047200202941895, "end": **********.344452, "relative_end": **********.344452, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.345018, "relative_start": 1.2052860260009766, "end": **********.345018, "relative_end": **********.345018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::acbc3e3e19c70a7ebb0e7a5d98d84fbe", "start": **********.34683, "relative_start": 1.2070980072021484, "end": **********.34683, "relative_end": **********.34683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.347393, "relative_start": 1.2076611518859863, "end": **********.347393, "relative_end": **********.347393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.348037, "relative_start": 1.2083051204681396, "end": **********.348037, "relative_end": **********.348037, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6e0b6ed9bf49c6ad9d02af2c7f911103", "start": **********.34989, "relative_start": 1.210158109664917, "end": **********.34989, "relative_end": **********.34989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.350581, "relative_start": 1.2108490467071533, "end": **********.350581, "relative_end": **********.350581, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.35139, "relative_start": 1.211658000946045, "end": **********.35139, "relative_end": **********.35139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dac323985d9d2618ad252313442aaf03", "start": **********.353716, "relative_start": 1.2139840126037598, "end": **********.353716, "relative_end": **********.353716, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.354387, "relative_start": 1.2146551609039307, "end": **********.354387, "relative_end": **********.354387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::298cd8a12b86a6f371ff06491a0822fa", "start": **********.35584, "relative_start": 1.2161080837249756, "end": **********.35584, "relative_end": **********.35584, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.356478, "relative_start": 1.2167460918426514, "end": **********.356478, "relative_end": **********.356478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c59870a61b233f0766e3260625bdb025", "start": **********.358054, "relative_start": 1.2183220386505127, "end": **********.358054, "relative_end": **********.358054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.358647, "relative_start": 1.2189152240753174, "end": **********.358647, "relative_end": **********.358647, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3890eca5d46a147ef99ddf994453d2ec", "start": **********.3603, "relative_start": 1.2205681800842285, "end": **********.3603, "relative_end": **********.3603, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.360968, "relative_start": 1.221236228942871, "end": **********.360968, "relative_end": **********.360968, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ff1fde71531b073b2c658173537aaea5", "start": **********.362677, "relative_start": 1.222945213317871, "end": **********.362677, "relative_end": **********.362677, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.363389, "relative_start": 1.2236571311950684, "end": **********.363389, "relative_end": **********.363389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5cef0de51e1489c31c7fcb5d7f2f6a97", "start": **********.365104, "relative_start": 1.225372076034546, "end": **********.365104, "relative_end": **********.365104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.36596, "relative_start": 1.2262279987335205, "end": **********.36596, "relative_end": **********.36596, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::89cb89d3fdb0a0a12f8aa61073c231e6", "start": **********.367923, "relative_start": 1.2281911373138428, "end": **********.367923, "relative_end": **********.367923, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.368557, "relative_start": 1.2288250923156738, "end": **********.368557, "relative_end": **********.368557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5a5b09d3f2ee0ddb2b536feb532d230a", "start": **********.370327, "relative_start": 1.2305951118469238, "end": **********.370327, "relative_end": **********.370327, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.370953, "relative_start": 1.2312211990356445, "end": **********.370953, "relative_end": **********.370953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fedc652debeb23dcbb31a98830baa397", "start": **********.372628, "relative_start": 1.232896089553833, "end": **********.372628, "relative_end": **********.372628, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.373247, "relative_start": 1.2335150241851807, "end": **********.373247, "relative_end": **********.373247, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.373959, "relative_start": 1.234227180480957, "end": **********.373959, "relative_end": **********.373959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ff3b2cf4e42e74e63db76ff05c5f2374", "start": **********.376065, "relative_start": 1.2363331317901611, "end": **********.376065, "relative_end": **********.376065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.376883, "relative_start": 1.2371511459350586, "end": **********.376883, "relative_end": **********.376883, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.377816, "relative_start": 1.238084077835083, "end": **********.377816, "relative_end": **********.377816, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::745871da7c635a3f461dfaeeef54a48e", "start": **********.37983, "relative_start": 1.240097999572754, "end": **********.37983, "relative_end": **********.37983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.380431, "relative_start": 1.240699052810669, "end": **********.380431, "relative_end": **********.380431, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d6f928aaf1e585d3246cb3bee8fdd45", "start": **********.381718, "relative_start": 1.2419860363006592, "end": **********.381718, "relative_end": **********.381718, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.382237, "relative_start": 1.2425050735473633, "end": **********.382237, "relative_end": **********.382237, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.382919, "relative_start": 1.2431871891021729, "end": **********.382919, "relative_end": **********.382919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2b3233eda7e50501ef45fd875b12da49", "start": **********.384332, "relative_start": 1.2446000576019287, "end": **********.384332, "relative_end": **********.384332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.385405, "relative_start": 1.2456731796264648, "end": **********.385405, "relative_end": **********.385405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.386165, "relative_start": 1.2464330196380615, "end": **********.386165, "relative_end": **********.386165, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b13663c834a4ae876ef8f72aa0610e8c", "start": **********.387654, "relative_start": 1.2479221820831299, "end": **********.387654, "relative_end": **********.387654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.page-header", "start": **********.388502, "relative_start": 1.248769998550415, "end": **********.388502, "relative_end": **********.388502, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::breadcrumb", "start": **********.388996, "relative_start": 1.2492640018463135, "end": **********.388996, "relative_end": **********.388996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.footer", "start": **********.390024, "relative_start": 1.2502920627593994, "end": **********.390024, "relative_end": **********.390024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.copyright", "start": **********.390505, "relative_start": 1.2507731914520264, "end": **********.390505, "relative_end": **********.390505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.after-content", "start": **********.391571, "relative_start": 1.2518391609191895, "end": **********.391571, "relative_end": **********.391571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::global-search.form", "start": **********.392091, "relative_start": 1.25235915184021, "end": **********.392091, "relative_end": **********.392091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3cec1c87224222bda738c53f782c5bc1", "start": **********.394734, "relative_start": 1.2550020217895508, "end": **********.394734, "relative_end": **********.394734, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.text-input", "start": **********.395126, "relative_start": 1.2553942203521729, "end": **********.395126, "relative_end": **********.395126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.39637, "relative_start": 1.2566380500793457, "end": **********.39637, "relative_end": **********.39637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.error", "start": **********.396909, "relative_start": 1.2571771144866943, "end": **********.396909, "relative_end": **********.396909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": **********.397344, "relative_start": 1.2576122283935547, "end": **********.397344, "relative_end": **********.397344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.index", "start": **********.397864, "relative_start": 1.2581322193145752, "end": **********.397864, "relative_end": **********.397864, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::414e4e803eeec6389552bb46515583c5", "start": **********.39919, "relative_start": 1.259458065032959, "end": **********.39919, "relative_end": **********.39919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c47a448d99d5719cb034f7947c739ff8", "start": **********.4004, "relative_start": 1.2606680393218994, "end": **********.4004, "relative_end": **********.4004, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e226165e1fca7eccb10f6857d7cd235a", "start": **********.40165, "relative_start": 1.261918067932129, "end": **********.40165, "relative_end": **********.40165, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal", "start": **********.402051, "relative_start": 1.2623190879821777, "end": **********.402051, "relative_end": **********.402051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::custom-template", "start": **********.403094, "relative_start": 1.263362169265747, "end": **********.403094, "relative_end": **********.403094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/media::partials.media", "start": **********.403721, "relative_start": 1.2639892101287842, "end": **********.403721, "relative_end": **********.403721, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close-button", "start": **********.404646, "relative_start": 1.2649140357971191, "end": **********.404646, "relative_end": **********.404646, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::loading", "start": **********.405065, "relative_start": 1.2653331756591797, "end": **********.405065, "relative_end": **********.405065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.text-input", "start": **********.405485, "relative_start": 1.2657530307769775, "end": **********.405485, "relative_end": **********.405485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.406224, "relative_start": 1.2664921283721924, "end": **********.406224, "relative_end": **********.406224, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.error", "start": **********.40659, "relative_start": 1.2668581008911133, "end": **********.40659, "relative_end": **********.40659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": **********.40695, "relative_start": 1.2672181129455566, "end": **********.40695, "relative_end": **********.40695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.407441, "relative_start": 1.2677090167999268, "end": **********.407441, "relative_end": **********.407441, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.408502, "relative_start": 1.2687702178955078, "end": **********.408502, "relative_end": **********.408502, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.409096, "relative_start": 1.2693641185760498, "end": **********.409096, "relative_end": **********.409096, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal", "start": **********.409615, "relative_start": 1.269883155822754, "end": **********.409615, "relative_end": **********.409615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close-button", "start": **********.410491, "relative_start": 1.270759105682373, "end": **********.410491, "relative_end": **********.410491, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/media::config", "start": **********.411086, "relative_start": 1.2713541984558105, "end": **********.411086, "relative_end": **********.411086, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::debug-badge", "start": **********.610012, "relative_start": 1.4702801704406738, "end": **********.610012, "relative_end": **********.610012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.action", "start": **********.61085, "relative_start": 1.4711182117462158, "end": **********.61085, "relative_end": **********.61085, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.alert", "start": **********.611706, "relative_start": 1.4719741344451904, "end": **********.611706, "relative_end": **********.611706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close-button", "start": **********.612385, "relative_start": 1.4726531505584717, "end": **********.612385, "relative_end": **********.612385, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dcf17957c4aa053a618fd9c312cc29fc", "start": **********.613219, "relative_start": 1.473487138748169, "end": **********.613219, "relative_end": **********.613219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal", "start": **********.613495, "relative_start": 1.4737632274627686, "end": **********.613495, "relative_end": **********.613495, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.action", "start": **********.614275, "relative_start": 1.4745430946350098, "end": **********.614275, "relative_end": **********.614275, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.alert", "start": **********.614833, "relative_start": 1.4751012325286865, "end": **********.614833, "relative_end": **********.614833, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close-button", "start": **********.6154, "relative_start": 1.47566819190979, "end": **********.6154, "relative_end": **********.6154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fcfb9f9ba5fb460899f38b71f491e1fe", "start": **********.616138, "relative_start": 1.4764060974121094, "end": **********.616138, "relative_end": **********.616138, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal", "start": **********.61641, "relative_start": 1.4766781330108643, "end": **********.61641, "relative_end": **********.61641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::layouts.base", "start": **********.617214, "relative_start": 1.4774820804595947, "end": **********.617214, "relative_end": **********.617214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.layouts.header", "start": **********.617983, "relative_start": 1.4782512187957764, "end": **********.617983, "relative_end": **********.617983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: assets::header", "start": **********.621252, "relative_start": 1.481520175933838, "end": **********.621252, "relative_end": **********.621252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.common", "start": **********.623264, "relative_start": 1.483532190322876, "end": **********.623264, "relative_end": **********.623264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: assets::footer", "start": **********.625818, "relative_start": 1.486086130142212, "end": **********.625818, "relative_end": **********.625818, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::notification.notification", "start": **********.626923, "relative_start": 1.4871912002563477, "end": **********.626923, "relative_end": **********.626923, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.627982, "relative_start": 1.4882500171661377, "end": **********.628852, "relative_end": **********.628852, "duration": 0.0008699893951416016, "duration_str": "870μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 65797312, "peak_usage_str": "63MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "lcoal", "Debug Mode": "Enabled", "URL": "tesmods.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 327, "nb_templates": 327, "templates": [{"name": "1x plugins/ecommerce::product-options.option-admin", "param_count": null, "params": [], "start": **********.997891, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/plugins/ecommerce/resources/views/product-options/option-admin.blade.phpplugins/ecommerce::product-options.option-admin", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fproduct-options%2Foption-admin.blade.php&line=1", "ajax": false, "filename": "option-admin.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/ecommerce::product-options.option-admin"}, {"name": "3x ********************************::table.header.cell", "param_count": null, "params": [], "start": **********.001875, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/table/header/cell.blade.php********************************::table.header.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fheader%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::table.header.cell"}, {"name": "1x ********************************::table.header.index", "param_count": null, "params": [], "start": **********.002623, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/table/header/index.blade.php********************************::table.header.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::table.header.index"}, {"name": "1x __components::d634f8cc23c050fb53f76b965496cac0", "param_count": null, "params": [], "start": **********.005312, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/d634f8cc23c050fb53f76b965496cac0.blade.php__components::d634f8cc23c050fb53f76b965496cac0", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Fd634f8cc23c050fb53f76b965496cac0.blade.php&line=1", "ajax": false, "filename": "d634f8cc23c050fb53f76b965496cac0.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::d634f8cc23c050fb53f76b965496cac0"}, {"name": "5x ********************************::table.body.cell", "param_count": null, "params": [], "start": **********.005637, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/table/body/cell.blade.php********************************::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}, "render_count": 5, "name_original": "********************************::table.body.cell"}, {"name": "9x ********************************::button", "param_count": null, "params": [], "start": **********.006622, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/button.blade.php********************************::button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 9, "name_original": "********************************::button"}, {"name": "1x __components::e38e2f7375f7e06238efe5faa11bb3f5", "param_count": null, "params": [], "start": **********.00785, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/e38e2f7375f7e06238efe5faa11bb3f5.blade.php__components::e38e2f7375f7e06238efe5faa11bb3f5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Fe38e2f7375f7e06238efe5faa11bb3f5.blade.php&line=1", "ajax": false, "filename": "e38e2f7375f7e06238efe5faa11bb3f5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e38e2f7375f7e06238efe5faa11bb3f5"}, {"name": "1x ********************************::table.body.row", "param_count": null, "params": [], "start": **********.00838, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/table/body/row.blade.php********************************::table.body.row", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::table.body.row"}, {"name": "1x ********************************::table.body.index", "param_count": null, "params": [], "start": **********.008736, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/table/body/index.blade.php********************************::table.body.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::table.body.index"}, {"name": "1x ********************************::table.index", "param_count": null, "params": [], "start": **********.009248, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/table/index.blade.php********************************::table.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::table.index"}, {"name": "1x core/base::forms.form", "param_count": null, "params": [], "start": **********.013773, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/form.blade.phpcore/base::forms.form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.form"}, {"name": "1x core/base::forms.fields.text", "param_count": null, "params": [], "start": **********.017873, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/fields/text.blade.phpcore/base::forms.fields.text", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Ftext.blade.php&line=1", "ajax": false, "filename": "text.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.text"}, {"name": "3x core/base::forms.partials.label", "param_count": null, "params": [], "start": **********.018564, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/partials/label.blade.phpcore/base::forms.partials.label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 3, "name_original": "core/base::forms.partials.label"}, {"name": "6x ********************************::form.label", "param_count": null, "params": [], "start": **********.019117, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/form/label.blade.php********************************::form.label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::form.label"}, {"name": "3x ********************************::form.field", "param_count": null, "params": [], "start": **********.020476, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/form/field.blade.php********************************::form.field", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::form.field"}, {"name": "3x core/base::forms.partials.help-block", "param_count": null, "params": [], "start": **********.021131, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/partials/help-block.blade.phpcore/base::forms.partials.help-block", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fhelp-block.blade.php&line=1", "ajax": false, "filename": "help-block.blade.php", "line": "?"}, "render_count": 3, "name_original": "core/base::forms.partials.help-block"}, {"name": "3x core/base::forms.partials.errors", "param_count": null, "params": [], "start": **********.021532, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/partials/errors.blade.phpcore/base::forms.partials.errors", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferrors.blade.php&line=1", "ajax": false, "filename": "errors.blade.php", "line": "?"}, "render_count": 3, "name_original": "core/base::forms.partials.errors"}, {"name": "3x core/base::forms.columns.column-span", "param_count": null, "params": [], "start": **********.022047, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/columns/column-span.blade.phpcore/base::forms.columns.column-span", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fcolumns%2Fcolumn-span.blade.php&line=1", "ajax": false, "filename": "column-span.blade.php", "line": "?"}, "render_count": 3, "name_original": "core/base::forms.columns.column-span"}, {"name": "3x ********************************::card.body.index", "param_count": null, "params": [], "start": **********.022771, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/card/body/index.blade.php********************************::card.body.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Fbody%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::card.body.index"}, {"name": "6x ********************************::card.index", "param_count": null, "params": [], "start": **********.023113, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/card/index.blade.php********************************::card.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::card.index"}, {"name": "1x core/base::forms.partials.meta-box", "param_count": null, "params": [], "start": **********.023601, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/partials/meta-box.blade.phpcore/base::forms.partials.meta-box", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fmeta-box.blade.php&line=1", "ajax": false, "filename": "meta-box.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.meta-box"}, {"name": "5x ********************************::card.title", "param_count": null, "params": [], "start": **********.024686, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/card/title.blade.php********************************::card.title", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Ftitle.blade.php&line=1", "ajax": false, "filename": "title.blade.php", "line": "?"}, "render_count": 5, "name_original": "********************************::card.title"}, {"name": "5x ********************************::card.header.index", "param_count": null, "params": [], "start": **********.025132, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/card/header/index.blade.php********************************::card.header.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 5, "name_original": "********************************::card.header.index"}, {"name": "3x core/base::elements.meta-box", "param_count": null, "params": [], "start": **********.028027, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/elements/meta-box.blade.phpcore/base::elements.meta-box", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Felements%2Fmeta-box.blade.php&line=1", "ajax": false, "filename": "meta-box.blade.php", "line": "?"}, "render_count": 3, "name_original": "core/base::elements.meta-box"}, {"name": "1x core/base::forms.partials.form-actions", "param_count": null, "params": [], "start": **********.028532, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/partials/form-actions.blade.phpcore/base::forms.partials.form-actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fform-actions.blade.php&line=1", "ajax": false, "filename": "form-actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.form-actions"}, {"name": "2x core/base::forms.partials.form-buttons", "param_count": null, "params": [], "start": **********.030433, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/partials/form-buttons.blade.phpcore/base::forms.partials.form-buttons", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fform-buttons.blade.php&line=1", "ajax": false, "filename": "form-buttons.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.partials.form-buttons"}, {"name": "2x __components::d25e365b684c7fa9ad5ec13cfb768fc1", "param_count": null, "params": [], "start": **********.032361, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/d25e365b684c7fa9ad5ec13cfb768fc1.blade.php__components::d25e365b684c7fa9ad5ec13cfb768fc1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Fd25e365b684c7fa9ad5ec13cfb768fc1.blade.php&line=1", "ajax": false, "filename": "d25e365b684c7fa9ad5ec13cfb768fc1.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::d25e365b684c7fa9ad5ec13cfb768fc1"}, {"name": "2x __components::5db12cde11beb11dd9ef0499874ec197", "param_count": null, "params": [], "start": **********.033719, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/5db12cde11beb11dd9ef0499874ec197.blade.php__components::5db12cde11beb11dd9ef0499874ec197", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F5db12cde11beb11dd9ef0499874ec197.blade.php&line=1", "ajax": false, "filename": "5db12cde11beb11dd9ef0499874ec197.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::5db12cde11beb11dd9ef0499874ec197"}, {"name": "1x core/base::layouts.partials.breadcrumbs", "param_count": null, "params": [], "start": **********.037438, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/layouts/partials/breadcrumbs.blade.phpcore/base::layouts.partials.breadcrumbs", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fbreadcrumbs.blade.php&line=1", "ajax": false, "filename": "breadcrumbs.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.breadcrumbs"}, {"name": "1x core/base::forms.fields.custom-select", "param_count": null, "params": [], "start": **********.044831, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/fields/custom-select.blade.phpcore/base::forms.fields.custom-select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fcustom-select.blade.php&line=1", "ajax": false, "filename": "custom-select.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.custom-select"}, {"name": "1x core/base::forms.partials.custom-select", "param_count": null, "params": [], "start": **********.04599, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/partials/custom-select.blade.phpcore/base::forms.partials.custom-select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fcustom-select.blade.php&line=1", "ajax": false, "filename": "custom-select.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.custom-select"}, {"name": "1x core/base::forms.fields.on-off", "param_count": null, "params": [], "start": **********.051386, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/fields/on-off.blade.phpcore/base::forms.fields.on-off", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fon-off.blade.php&line=1", "ajax": false, "filename": "on-off.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.on-off"}, {"name": "1x core/base::forms.partials.on-off", "param_count": null, "params": [], "start": **********.052348, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/partials/on-off.blade.phpcore/base::forms.partials.on-off", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fon-off.blade.php&line=1", "ajax": false, "filename": "on-off.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.on-off"}, {"name": "1x ********************************::form.toggle", "param_count": null, "params": [], "start": **********.053193, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/form/toggle.blade.php********************************::form.toggle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ftoggle.blade.php&line=1", "ajax": false, "filename": "toggle.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form.toggle"}, {"name": "1x core/js-validation::bootstrap", "param_count": null, "params": [], "start": **********.085676, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/js-validation/resources/views/bootstrap.blade.phpcore/js-validation::bootstrap", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fjs-validation%2Fresources%2Fviews%2Fbootstrap.blade.php&line=1", "ajax": false, "filename": "bootstrap.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/js-validation::bootstrap"}, {"name": "1x core/base::layouts.master", "param_count": null, "params": [], "start": **********.086978, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/layouts/master.blade.phpcore/base::layouts.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.master"}, {"name": "1x core/base::layouts.vertical.partials.before-content", "param_count": null, "params": [], "start": **********.088365, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/layouts/vertical/partials/before-content.blade.phpcore/base::layouts.vertical.partials.before-content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fbefore-content.blade.php&line=1", "ajax": false, "filename": "before-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.before-content"}, {"name": "1x core/base::layouts.vertical.partials.header", "param_count": null, "params": [], "start": **********.089076, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/layouts/vertical/partials/header.blade.phpcore/base::layouts.vertical.partials.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.header"}, {"name": "2x __components::133aa97c11fca0f84f02ebcb9fd067dc", "param_count": null, "params": [], "start": **********.091563, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/133aa97c11fca0f84f02ebcb9fd067dc.blade.php__components::133aa97c11fca0f84f02ebcb9fd067dc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F133aa97c11fca0f84f02ebcb9fd067dc.blade.php&line=1", "ajax": false, "filename": "133aa97c11fca0f84f02ebcb9fd067dc.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::133aa97c11fca0f84f02ebcb9fd067dc"}, {"name": "2x core/base::partials.logo", "param_count": null, "params": [], "start": **********.092214, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/partials/logo.blade.phpcore/base::partials.logo", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::partials.logo"}, {"name": "1x core/base::global-search.navbar-input", "param_count": null, "params": [], "start": **********.107299, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/global-search/navbar-input.blade.phpcore/base::global-search.navbar-input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fglobal-search%2Fnavbar-input.blade.php&line=1", "ajax": false, "filename": "navbar-input.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::global-search.navbar-input"}, {"name": "3x ********************************::form.text-input", "param_count": null, "params": [], "start": **********.107954, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/form/text-input.blade.php********************************::form.text-input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ftext-input.blade.php&line=1", "ajax": false, "filename": "text-input.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::form.text-input"}, {"name": "3x ********************************::form.error", "param_count": null, "params": [], "start": **********.110244, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/form/error.blade.php********************************::form.error", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::form.error"}, {"name": "3x ********************************::form-group", "param_count": null, "params": [], "start": **********.111443, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/form-group.blade.php********************************::form-group", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform-group.blade.php&line=1", "ajax": false, "filename": "form-group.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::form-group"}, {"name": "1x __components::dc988bb05638c97d86c3bc8a9b727e31", "param_count": null, "params": [], "start": **********.114417, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/dc988bb05638c97d86c3bc8a9b727e31.blade.php__components::dc988bb05638c97d86c3bc8a9b727e31", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Fdc988bb05638c97d86c3bc8a9b727e31.blade.php&line=1", "ajax": false, "filename": "dc988bb05638c97d86c3bc8a9b727e31.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dc988bb05638c97d86c3bc8a9b727e31"}, {"name": "1x core/base::layouts.partials.theme-toggle", "param_count": null, "params": [], "start": **********.114824, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/layouts/partials/theme-toggle.blade.phpcore/base::layouts.partials.theme-toggle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Ftheme-toggle.blade.php&line=1", "ajax": false, "filename": "theme-toggle.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.theme-toggle"}, {"name": "1x __components::ec711b4f47c4b42ebc81a7564c1d8d33", "param_count": null, "params": [], "start": **********.116364, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/ec711b4f47c4b42ebc81a7564c1d8d33.blade.php__components::ec711b4f47c4b42ebc81a7564c1d8d33", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Fec711b4f47c4b42ebc81a7564c1d8d33.blade.php&line=1", "ajax": false, "filename": "ec711b4f47c4b42ebc81a7564c1d8d33.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ec711b4f47c4b42ebc81a7564c1d8d33"}, {"name": "1x core/base::notification.nav-item", "param_count": null, "params": [], "start": **********.118345, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/notification/nav-item.blade.phpcore/base::notification.nav-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fnotification%2Fnav-item.blade.php&line=1", "ajax": false, "filename": "nav-item.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::notification.nav-item"}, {"name": "1x __components::306e03d3dc5634ee2e82192f553c6f9b", "param_count": null, "params": [], "start": **********.119835, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/306e03d3dc5634ee2e82192f553c6f9b.blade.php__components::306e03d3dc5634ee2e82192f553c6f9b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F306e03d3dc5634ee2e82192f553c6f9b.blade.php&line=1", "ajax": false, "filename": "306e03d3dc5634ee2e82192f553c6f9b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::306e03d3dc5634ee2e82192f553c6f9b"}, {"name": "1x plugins/contact::partials.notification", "param_count": null, "params": [], "start": **********.123422, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/plugins/contact/resources/views/partials/notification.blade.phpplugins/contact::partials.notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Fcontact%2Fresources%2Fviews%2Fpartials%2Fnotification.blade.php&line=1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/contact::partials.notification"}, {"name": "3x __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "param_count": null, "params": [], "start": **********.124931, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php__components::98e88d58787b8dfeb6f0d1dc0a785cfd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php&line=1", "ajax": false, "filename": "98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::98e88d58787b8dfeb6f0d1dc0a785cfd"}, {"name": "1x ********************************::card.actions", "param_count": null, "params": [], "start": **********.127024, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/card/actions.blade.php********************************::card.actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Factions.blade.php&line=1", "ajax": false, "filename": "actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::card.actions"}, {"name": "1x core/base::layouts.partials.user-menu", "param_count": null, "params": [], "start": **********.170416, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/layouts/partials/user-menu.blade.phpcore/base::layouts.partials.user-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fuser-menu.blade.php&line=1", "ajax": false, "filename": "user-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.user-menu"}, {"name": "4x ********************************::dropdown.item", "param_count": null, "params": [], "start": **********.178071, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/dropdown/item.blade.php********************************::dropdown.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::dropdown.item"}, {"name": "2x __components::2907df26a6102c24ab0c37391217b338", "param_count": null, "params": [], "start": **********.179481, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/2907df26a6102c24ab0c37391217b338.blade.php__components::2907df26a6102c24ab0c37391217b338", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F2907df26a6102c24ab0c37391217b338.blade.php&line=1", "ajax": false, "filename": "2907df26a6102c24ab0c37391217b338.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::2907df26a6102c24ab0c37391217b338"}, {"name": "2x __components::f38bffca7b8a1a50e97a6950ffd66c5c", "param_count": null, "params": [], "start": **********.181321, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/f38bffca7b8a1a50e97a6950ffd66c5c.blade.php__components::f38bffca7b8a1a50e97a6950ffd66c5c", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Ff38bffca7b8a1a50e97a6950ffd66c5c.blade.php&line=1", "ajax": false, "filename": "f38bffca7b8a1a50e97a6950ffd66c5c.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::f38bffca7b8a1a50e97a6950ffd66c5c"}, {"name": "2x ********************************::dropdown.index", "param_count": null, "params": [], "start": **********.181806, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/dropdown/index.blade.php********************************::dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::dropdown.index"}, {"name": "1x core/base::layouts.vertical.partials.aside", "param_count": null, "params": [], "start": **********.182986, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/layouts/vertical/partials/aside.blade.phpcore/base::layouts.vertical.partials.aside", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Faside.blade.php&line=1", "ajax": false, "filename": "aside.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.aside"}, {"name": "1x core/base::layouts.vertical.partials.sidebar", "param_count": null, "params": [], "start": **********.191255, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/layouts/vertical/partials/sidebar.blade.phpcore/base::layouts.vertical.partials.sidebar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.sidebar"}, {"name": "1x core/base::layouts.partials.navbar-nav", "param_count": null, "params": [], "start": **********.191811, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/layouts/partials/navbar-nav.blade.phpcore/base::layouts.partials.navbar-nav", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav.blade.php&line=1", "ajax": false, "filename": "navbar-nav.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.navbar-nav"}, {"name": "21x core/base::layouts.partials.navbar-nav-item", "param_count": null, "params": [], "start": **********.208862, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/layouts/partials/navbar-nav-item.blade.phpcore/base::layouts.partials.navbar-nav-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav-item.blade.php&line=1", "ajax": false, "filename": "navbar-nav-item.blade.php", "line": "?"}, "render_count": 21, "name_original": "core/base::layouts.partials.navbar-nav-item"}, {"name": "67x core/base::layouts.partials.navbar-nav-item-link", "param_count": null, "params": [], "start": **********.210127, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/layouts/partials/navbar-nav-item-link.blade.phpcore/base::layouts.partials.navbar-nav-item-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav-item-link.blade.php&line=1", "ajax": false, "filename": "navbar-nav-item-link.blade.php", "line": "?"}, "render_count": 67, "name_original": "core/base::layouts.partials.navbar-nav-item-link"}, {"name": "1x __components::af68cda57c5ca67f3b8a7729953880bc", "param_count": null, "params": [], "start": **********.212878, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/af68cda57c5ca67f3b8a7729953880bc.blade.php__components::af68cda57c5ca67f3b8a7729953880bc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Faf68cda57c5ca67f3b8a7729953880bc.blade.php&line=1", "ajax": false, "filename": "af68cda57c5ca67f3b8a7729953880bc.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::af68cda57c5ca67f3b8a7729953880bc"}, {"name": "1x __components::c50817c59f218fea1b6c19be4db347dd", "param_count": null, "params": [], "start": **********.216956, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/c50817c59f218fea1b6c19be4db347dd.blade.php__components::c50817c59f218fea1b6c19be4db347dd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Fc50817c59f218fea1b6c19be4db347dd.blade.php&line=1", "ajax": false, "filename": "c50817c59f218fea1b6c19be4db347dd.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c50817c59f218fea1b6c19be4db347dd"}, {"name": "6x core/base::partials.navbar.badge-count", "param_count": null, "params": [], "start": **********.217751, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/partials/navbar/badge-count.blade.phpcore/base::partials.navbar.badge-count", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Fnavbar%2Fbadge-count.blade.php&line=1", "ajax": false, "filename": "badge-count.blade.php", "line": "?"}, "render_count": 6, "name_original": "core/base::partials.navbar.badge-count"}, {"name": "6x ********************************::navbar.badge-count", "param_count": null, "params": [], "start": **********.218359, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/navbar/badge-count.blade.php********************************::navbar.badge-count", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fnavbar%2Fbadge-count.blade.php&line=1", "ajax": false, "filename": "badge-count.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::navbar.badge-count"}, {"name": "1x __components::42d71f4fe12493bd6829a606380e1435", "param_count": null, "params": [], "start": **********.220942, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/42d71f4fe12493bd6829a606380e1435.blade.php__components::42d71f4fe12493bd6829a606380e1435", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F42d71f4fe12493bd6829a606380e1435.blade.php&line=1", "ajax": false, "filename": "42d71f4fe12493bd6829a606380e1435.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::42d71f4fe12493bd6829a606380e1435"}, {"name": "1x __components::1a65d1071503a2e05e73a706bfebac58", "param_count": null, "params": [], "start": **********.224885, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/1a65d1071503a2e05e73a706bfebac58.blade.php__components::1a65d1071503a2e05e73a706bfebac58", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F1a65d1071503a2e05e73a706bfebac58.blade.php&line=1", "ajax": false, "filename": "1a65d1071503a2e05e73a706bfebac58.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1a65d1071503a2e05e73a706bfebac58"}, {"name": "1x __components::7e33c8bf87ccc47b22832a4eb415ca9d", "param_count": null, "params": [], "start": **********.227927, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/7e33c8bf87ccc47b22832a4eb415ca9d.blade.php__components::7e33c8bf87ccc47b22832a4eb415ca9d", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F7e33c8bf87ccc47b22832a4eb415ca9d.blade.php&line=1", "ajax": false, "filename": "7e33c8bf87ccc47b22832a4eb415ca9d.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7e33c8bf87ccc47b22832a4eb415ca9d"}, {"name": "1x __components::452ccf81d5bbacec7ec062999540e293", "param_count": null, "params": [], "start": **********.230717, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/452ccf81d5bbacec7ec062999540e293.blade.php__components::452ccf81d5bbacec7ec062999540e293", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F452ccf81d5bbacec7ec062999540e293.blade.php&line=1", "ajax": false, "filename": "452ccf81d5bbacec7ec062999540e293.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::452ccf81d5bbacec7ec062999540e293"}, {"name": "1x __components::ee52ba22bc1262334c4146935fbed227", "param_count": null, "params": [], "start": **********.234588, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/ee52ba22bc1262334c4146935fbed227.blade.php__components::ee52ba22bc1262334c4146935fbed227", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Fee52ba22bc1262334c4146935fbed227.blade.php&line=1", "ajax": false, "filename": "ee52ba22bc1262334c4146935fbed227.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ee52ba22bc1262334c4146935fbed227"}, {"name": "1x __components::7bd0601fe0dc4645b269458a9ed5f144", "param_count": null, "params": [], "start": **********.237198, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/7bd0601fe0dc4645b269458a9ed5f144.blade.php__components::7bd0601fe0dc4645b269458a9ed5f144", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F7bd0601fe0dc4645b269458a9ed5f144.blade.php&line=1", "ajax": false, "filename": "7bd0601fe0dc4645b269458a9ed5f144.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7bd0601fe0dc4645b269458a9ed5f144"}, {"name": "1x __components::5e1ed162565cf31bd543a8427caaef1e", "param_count": null, "params": [], "start": **********.239374, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/5e1ed162565cf31bd543a8427caaef1e.blade.php__components::5e1ed162565cf31bd543a8427caaef1e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F5e1ed162565cf31bd543a8427caaef1e.blade.php&line=1", "ajax": false, "filename": "5e1ed162565cf31bd543a8427caaef1e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5e1ed162565cf31bd543a8427caaef1e"}, {"name": "1x __components::e3de92deb18074b63f585befac7d0965", "param_count": null, "params": [], "start": **********.241897, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/e3de92deb18074b63f585befac7d0965.blade.php__components::e3de92deb18074b63f585befac7d0965", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Fe3de92deb18074b63f585befac7d0965.blade.php&line=1", "ajax": false, "filename": "e3de92deb18074b63f585befac7d0965.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e3de92deb18074b63f585befac7d0965"}, {"name": "1x __components::69ab4d8cea5c292eefba375a85e9768a", "param_count": null, "params": [], "start": **********.245558, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/69ab4d8cea5c292eefba375a85e9768a.blade.php__components::69ab4d8cea5c292eefba375a85e9768a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F69ab4d8cea5c292eefba375a85e9768a.blade.php&line=1", "ajax": false, "filename": "69ab4d8cea5c292eefba375a85e9768a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::69ab4d8cea5c292eefba375a85e9768a"}, {"name": "1x __components::64f291ea9b97f679266b5e47f5ae8464", "param_count": null, "params": [], "start": **********.247701, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/64f291ea9b97f679266b5e47f5ae8464.blade.php__components::64f291ea9b97f679266b5e47f5ae8464", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F64f291ea9b97f679266b5e47f5ae8464.blade.php&line=1", "ajax": false, "filename": "64f291ea9b97f679266b5e47f5ae8464.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::64f291ea9b97f679266b5e47f5ae8464"}, {"name": "2x __components::9413e731e9bb6e320e018067130903e9", "param_count": null, "params": [], "start": **********.249792, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/9413e731e9bb6e320e018067130903e9.blade.php__components::9413e731e9bb6e320e018067130903e9", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F9413e731e9bb6e320e018067130903e9.blade.php&line=1", "ajax": false, "filename": "9413e731e9bb6e320e018067130903e9.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::9413e731e9bb6e320e018067130903e9"}, {"name": "2x __components::59b84d05fdb5c77612cfb696a101e213", "param_count": null, "params": [], "start": **********.252608, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/59b84d05fdb5c77612cfb696a101e213.blade.php__components::59b84d05fdb5c77612cfb696a101e213", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F59b84d05fdb5c77612cfb696a101e213.blade.php&line=1", "ajax": false, "filename": "59b84d05fdb5c77612cfb696a101e213.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::59b84d05fdb5c77612cfb696a101e213"}, {"name": "1x __components::2c23a08a8a248b19ed43094ec82cfb6b", "param_count": null, "params": [], "start": **********.254992, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/2c23a08a8a248b19ed43094ec82cfb6b.blade.php__components::2c23a08a8a248b19ed43094ec82cfb6b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F2c23a08a8a248b19ed43094ec82cfb6b.blade.php&line=1", "ajax": false, "filename": "2c23a08a8a248b19ed43094ec82cfb6b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2c23a08a8a248b19ed43094ec82cfb6b"}, {"name": "1x __components::1859ae2c66dc0538fd83aadc7f316844", "param_count": null, "params": [], "start": **********.262082, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/1859ae2c66dc0538fd83aadc7f316844.blade.php__components::1859ae2c66dc0538fd83aadc7f316844", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F1859ae2c66dc0538fd83aadc7f316844.blade.php&line=1", "ajax": false, "filename": "1859ae2c66dc0538fd83aadc7f316844.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1859ae2c66dc0538fd83aadc7f316844"}, {"name": "1x __components::ccda1407f97c36756562e2c01f09a7ab", "param_count": null, "params": [], "start": **********.264539, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/ccda1407f97c36756562e2c01f09a7ab.blade.php__components::ccda1407f97c36756562e2c01f09a7ab", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Fccda1407f97c36756562e2c01f09a7ab.blade.php&line=1", "ajax": false, "filename": "ccda1407f97c36756562e2c01f09a7ab.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ccda1407f97c36756562e2c01f09a7ab"}, {"name": "1x __components::52309442cd989852b03aa65d96b55790", "param_count": null, "params": [], "start": **********.26742, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/52309442cd989852b03aa65d96b55790.blade.php__components::52309442cd989852b03aa65d96b55790", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F52309442cd989852b03aa65d96b55790.blade.php&line=1", "ajax": false, "filename": "52309442cd989852b03aa65d96b55790.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::52309442cd989852b03aa65d96b55790"}, {"name": "1x __components::2cd2bd3a1a36cafc00007578f96771de", "param_count": null, "params": [], "start": **********.271607, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/2cd2bd3a1a36cafc00007578f96771de.blade.php__components::2cd2bd3a1a36cafc00007578f96771de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F2cd2bd3a1a36cafc00007578f96771de.blade.php&line=1", "ajax": false, "filename": "2cd2bd3a1a36cafc00007578f96771de.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2cd2bd3a1a36cafc00007578f96771de"}, {"name": "1x __components::24c50807435b9d6d46a8d65cd016c8b0", "param_count": null, "params": [], "start": **********.2739, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/24c50807435b9d6d46a8d65cd016c8b0.blade.php__components::24c50807435b9d6d46a8d65cd016c8b0", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F24c50807435b9d6d46a8d65cd016c8b0.blade.php&line=1", "ajax": false, "filename": "24c50807435b9d6d46a8d65cd016c8b0.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::24c50807435b9d6d46a8d65cd016c8b0"}, {"name": "2x __components::13365b7e5a448d13150fdb4b3884b510", "param_count": null, "params": [], "start": **********.276017, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/13365b7e5a448d13150fdb4b3884b510.blade.php__components::13365b7e5a448d13150fdb4b3884b510", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F13365b7e5a448d13150fdb4b3884b510.blade.php&line=1", "ajax": false, "filename": "13365b7e5a448d13150fdb4b3884b510.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::13365b7e5a448d13150fdb4b3884b510"}, {"name": "1x __components::dd4c2087b0a47210b5b4e3ee87ef3eca", "param_count": null, "params": [], "start": **********.27933, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/dd4c2087b0a47210b5b4e3ee87ef3eca.blade.php__components::dd4c2087b0a47210b5b4e3ee87ef3eca", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Fdd4c2087b0a47210b5b4e3ee87ef3eca.blade.php&line=1", "ajax": false, "filename": "dd4c2087b0a47210b5b4e3ee87ef3eca.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dd4c2087b0a47210b5b4e3ee87ef3eca"}, {"name": "1x __components::998e4178ecae37dacf7321232f455f64", "param_count": null, "params": [], "start": **********.281816, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/998e4178ecae37dacf7321232f455f64.blade.php__components::998e4178ecae37dacf7321232f455f64", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F998e4178ecae37dacf7321232f455f64.blade.php&line=1", "ajax": false, "filename": "998e4178ecae37dacf7321232f455f64.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::998e4178ecae37dacf7321232f455f64"}, {"name": "1x __components::06e7cfabd119917d6efbd595a344e37b", "param_count": null, "params": [], "start": **********.284034, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/06e7cfabd119917d6efbd595a344e37b.blade.php__components::06e7cfabd119917d6efbd595a344e37b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F06e7cfabd119917d6efbd595a344e37b.blade.php&line=1", "ajax": false, "filename": "06e7cfabd119917d6efbd595a344e37b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::06e7cfabd119917d6efbd595a344e37b"}, {"name": "3x __components::6e0b6ed9bf49c6ad9d02af2c7f911103", "param_count": null, "params": [], "start": **********.286249, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/6e0b6ed9bf49c6ad9d02af2c7f911103.blade.php__components::6e0b6ed9bf49c6ad9d02af2c7f911103", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F6e0b6ed9bf49c6ad9d02af2c7f911103.blade.php&line=1", "ajax": false, "filename": "6e0b6ed9bf49c6ad9d02af2c7f911103.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::6e0b6ed9bf49c6ad9d02af2c7f911103"}, {"name": "1x __components::ca20cd1247722214b06db9aa7c493b27", "param_count": null, "params": [], "start": **********.291371, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/ca20cd1247722214b06db9aa7c493b27.blade.php__components::ca20cd1247722214b06db9aa7c493b27", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Fca20cd1247722214b06db9aa7c493b27.blade.php&line=1", "ajax": false, "filename": "ca20cd1247722214b06db9aa7c493b27.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ca20cd1247722214b06db9aa7c493b27"}, {"name": "5x __components::d890ecc3acbc4ef41a8ece9e81698457", "param_count": null, "params": [], "start": **********.294674, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/d890ecc3acbc4ef41a8ece9e81698457.blade.php__components::d890ecc3acbc4ef41a8ece9e81698457", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Fd890ecc3acbc4ef41a8ece9e81698457.blade.php&line=1", "ajax": false, "filename": "d890ecc3acbc4ef41a8ece9e81698457.blade.php", "line": "?"}, "render_count": 5, "name_original": "__components::d890ecc3acbc4ef41a8ece9e81698457"}, {"name": "1x __components::df3c1e6b28f622836bfe7885f46c5f8b", "param_count": null, "params": [], "start": **********.300339, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/df3c1e6b28f622836bfe7885f46c5f8b.blade.php__components::df3c1e6b28f622836bfe7885f46c5f8b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Fdf3c1e6b28f622836bfe7885f46c5f8b.blade.php&line=1", "ajax": false, "filename": "df3c1e6b28f622836bfe7885f46c5f8b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::df3c1e6b28f622836bfe7885f46c5f8b"}, {"name": "1x __components::a3acd3bd206d0793e18d491720de886c", "param_count": null, "params": [], "start": **********.305312, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/a3acd3bd206d0793e18d491720de886c.blade.php__components::a3acd3bd206d0793e18d491720de886c", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Fa3acd3bd206d0793e18d491720de886c.blade.php&line=1", "ajax": false, "filename": "a3acd3bd206d0793e18d491720de886c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::a3acd3bd206d0793e18d491720de886c"}, {"name": "1x __components::fd978891e1ac33723cbffddc6658659a", "param_count": null, "params": [], "start": **********.308441, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/fd978891e1ac33723cbffddc6658659a.blade.php__components::fd978891e1ac33723cbffddc6658659a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Ffd978891e1ac33723cbffddc6658659a.blade.php&line=1", "ajax": false, "filename": "fd978891e1ac33723cbffddc6658659a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fd978891e1ac33723cbffddc6658659a"}, {"name": "1x __components::95c79b13976f7182ec015ce149dd5974", "param_count": null, "params": [], "start": **********.314604, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/95c79b13976f7182ec015ce149dd5974.blade.php__components::95c79b13976f7182ec015ce149dd5974", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F95c79b13976f7182ec015ce149dd5974.blade.php&line=1", "ajax": false, "filename": "95c79b13976f7182ec015ce149dd5974.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::95c79b13976f7182ec015ce149dd5974"}, {"name": "1x __components::cbce7a4c13e13a70eabb7759894a60fd", "param_count": null, "params": [], "start": **********.319004, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/cbce7a4c13e13a70eabb7759894a60fd.blade.php__components::cbce7a4c13e13a70eabb7759894a60fd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Fcbce7a4c13e13a70eabb7759894a60fd.blade.php&line=1", "ajax": false, "filename": "cbce7a4c13e13a70eabb7759894a60fd.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::cbce7a4c13e13a70eabb7759894a60fd"}, {"name": "1x __components::fe6fcb7551f99a7d9d1c5a4c0011f471", "param_count": null, "params": [], "start": **********.320746, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/fe6fcb7551f99a7d9d1c5a4c0011f471.blade.php__components::fe6fcb7551f99a7d9d1c5a4c0011f471", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Ffe6fcb7551f99a7d9d1c5a4c0011f471.blade.php&line=1", "ajax": false, "filename": "fe6fcb7551f99a7d9d1c5a4c0011f471.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fe6fcb7551f99a7d9d1c5a4c0011f471"}, {"name": "1x __components::e81d33e262b34e339fefa952b8ffa5f1", "param_count": null, "params": [], "start": **********.323297, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/e81d33e262b34e339fefa952b8ffa5f1.blade.php__components::e81d33e262b34e339fefa952b8ffa5f1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Fe81d33e262b34e339fefa952b8ffa5f1.blade.php&line=1", "ajax": false, "filename": "e81d33e262b34e339fefa952b8ffa5f1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e81d33e262b34e339fefa952b8ffa5f1"}, {"name": "1x __components::351bdfbe842eff22e08f1df9d5f5beb1", "param_count": null, "params": [], "start": **********.326439, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/351bdfbe842eff22e08f1df9d5f5beb1.blade.php__components::351bdfbe842eff22e08f1df9d5f5beb1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F351bdfbe842eff22e08f1df9d5f5beb1.blade.php&line=1", "ajax": false, "filename": "351bdfbe842eff22e08f1df9d5f5beb1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::351bdfbe842eff22e08f1df9d5f5beb1"}, {"name": "1x __components::fda51db955ba828a198ee1c8d52b2003", "param_count": null, "params": [], "start": **********.328408, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/fda51db955ba828a198ee1c8d52b2003.blade.php__components::fda51db955ba828a198ee1c8d52b2003", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Ffda51db955ba828a198ee1c8d52b2003.blade.php&line=1", "ajax": false, "filename": "fda51db955ba828a198ee1c8d52b2003.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fda51db955ba828a198ee1c8d52b2003"}, {"name": "1x __components::0343a1b0800146d7d9cf6a9514ec7bf4", "param_count": null, "params": [], "start": **********.335255, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/0343a1b0800146d7d9cf6a9514ec7bf4.blade.php__components::0343a1b0800146d7d9cf6a9514ec7bf4", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F0343a1b0800146d7d9cf6a9514ec7bf4.blade.php&line=1", "ajax": false, "filename": "0343a1b0800146d7d9cf6a9514ec7bf4.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0343a1b0800146d7d9cf6a9514ec7bf4"}, {"name": "1x __components::15422be7d0f2aaf8c8244c1e9db20ad9", "param_count": null, "params": [], "start": **********.337402, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/15422be7d0f2aaf8c8244c1e9db20ad9.blade.php__components::15422be7d0f2aaf8c8244c1e9db20ad9", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F15422be7d0f2aaf8c8244c1e9db20ad9.blade.php&line=1", "ajax": false, "filename": "15422be7d0f2aaf8c8244c1e9db20ad9.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::15422be7d0f2aaf8c8244c1e9db20ad9"}, {"name": "1x __components::2e5e965add6d0ee3aadb02ca38e70825", "param_count": null, "params": [], "start": **********.33966, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/2e5e965add6d0ee3aadb02ca38e70825.blade.php__components::2e5e965add6d0ee3aadb02ca38e70825", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F2e5e965add6d0ee3aadb02ca38e70825.blade.php&line=1", "ajax": false, "filename": "2e5e965add6d0ee3aadb02ca38e70825.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2e5e965add6d0ee3aadb02ca38e70825"}, {"name": "1x __components::5b8d99843e0f8eff6046d7236026b187", "param_count": null, "params": [], "start": **********.341853, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/5b8d99843e0f8eff6046d7236026b187.blade.php__components::5b8d99843e0f8eff6046d7236026b187", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F5b8d99843e0f8eff6046d7236026b187.blade.php&line=1", "ajax": false, "filename": "5b8d99843e0f8eff6046d7236026b187.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5b8d99843e0f8eff6046d7236026b187"}, {"name": "2x __components::1d6f928aaf1e585d3246cb3bee8fdd45", "param_count": null, "params": [], "start": **********.344429, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/1d6f928aaf1e585d3246cb3bee8fdd45.blade.php__components::1d6f928aaf1e585d3246cb3bee8fdd45", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F1d6f928aaf1e585d3246cb3bee8fdd45.blade.php&line=1", "ajax": false, "filename": "1d6f928aaf1e585d3246cb3bee8fdd45.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::1d6f928aaf1e585d3246cb3bee8fdd45"}, {"name": "1x __components::acbc3e3e19c70a7ebb0e7a5d98d84fbe", "param_count": null, "params": [], "start": **********.346811, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/acbc3e3e19c70a7ebb0e7a5d98d84fbe.blade.php__components::acbc3e3e19c70a7ebb0e7a5d98d84fbe", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Facbc3e3e19c70a7ebb0e7a5d98d84fbe.blade.php&line=1", "ajax": false, "filename": "acbc3e3e19c70a7ebb0e7a5d98d84fbe.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::acbc3e3e19c70a7ebb0e7a5d98d84fbe"}, {"name": "1x __components::dac323985d9d2618ad252313442aaf03", "param_count": null, "params": [], "start": **********.353675, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/dac323985d9d2618ad252313442aaf03.blade.php__components::dac323985d9d2618ad252313442aaf03", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Fdac323985d9d2618ad252313442aaf03.blade.php&line=1", "ajax": false, "filename": "dac323985d9d2618ad252313442aaf03.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dac323985d9d2618ad252313442aaf03"}, {"name": "1x __components::298cd8a12b86a6f371ff06491a0822fa", "param_count": null, "params": [], "start": **********.355819, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/298cd8a12b86a6f371ff06491a0822fa.blade.php__components::298cd8a12b86a6f371ff06491a0822fa", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F298cd8a12b86a6f371ff06491a0822fa.blade.php&line=1", "ajax": false, "filename": "298cd8a12b86a6f371ff06491a0822fa.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::298cd8a12b86a6f371ff06491a0822fa"}, {"name": "1x __components::c59870a61b233f0766e3260625bdb025", "param_count": null, "params": [], "start": **********.358033, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/c59870a61b233f0766e3260625bdb025.blade.php__components::c59870a61b233f0766e3260625bdb025", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Fc59870a61b233f0766e3260625bdb025.blade.php&line=1", "ajax": false, "filename": "c59870a61b233f0766e3260625bdb025.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c59870a61b233f0766e3260625bdb025"}, {"name": "1x __components::3890eca5d46a147ef99ddf994453d2ec", "param_count": null, "params": [], "start": **********.360277, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/3890eca5d46a147ef99ddf994453d2ec.blade.php__components::3890eca5d46a147ef99ddf994453d2ec", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F3890eca5d46a147ef99ddf994453d2ec.blade.php&line=1", "ajax": false, "filename": "3890eca5d46a147ef99ddf994453d2ec.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3890eca5d46a147ef99ddf994453d2ec"}, {"name": "1x __components::ff1fde71531b073b2c658173537aaea5", "param_count": null, "params": [], "start": **********.362645, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/ff1fde71531b073b2c658173537aaea5.blade.php__components::ff1fde71531b073b2c658173537aaea5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Fff1fde71531b073b2c658173537aaea5.blade.php&line=1", "ajax": false, "filename": "ff1fde71531b073b2c658173537aaea5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ff1fde71531b073b2c658173537aaea5"}, {"name": "1x __components::5cef0de51e1489c31c7fcb5d7f2f6a97", "param_count": null, "params": [], "start": **********.36508, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/5cef0de51e1489c31c7fcb5d7f2f6a97.blade.php__components::5cef0de51e1489c31c7fcb5d7f2f6a97", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F5cef0de51e1489c31c7fcb5d7f2f6a97.blade.php&line=1", "ajax": false, "filename": "5cef0de51e1489c31c7fcb5d7f2f6a97.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5cef0de51e1489c31c7fcb5d7f2f6a97"}, {"name": "1x __components::89cb89d3fdb0a0a12f8aa61073c231e6", "param_count": null, "params": [], "start": **********.367898, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/89cb89d3fdb0a0a12f8aa61073c231e6.blade.php__components::89cb89d3fdb0a0a12f8aa61073c231e6", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F89cb89d3fdb0a0a12f8aa61073c231e6.blade.php&line=1", "ajax": false, "filename": "89cb89d3fdb0a0a12f8aa61073c231e6.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::89cb89d3fdb0a0a12f8aa61073c231e6"}, {"name": "1x __components::5a5b09d3f2ee0ddb2b536feb532d230a", "param_count": null, "params": [], "start": **********.370294, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/5a5b09d3f2ee0ddb2b536feb532d230a.blade.php__components::5a5b09d3f2ee0ddb2b536feb532d230a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F5a5b09d3f2ee0ddb2b536feb532d230a.blade.php&line=1", "ajax": false, "filename": "5a5b09d3f2ee0ddb2b536feb532d230a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5a5b09d3f2ee0ddb2b536feb532d230a"}, {"name": "1x __components::fedc652debeb23dcbb31a98830baa397", "param_count": null, "params": [], "start": **********.372605, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/fedc652debeb23dcbb31a98830baa397.blade.php__components::fedc652debeb23dcbb31a98830baa397", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Ffedc652debeb23dcbb31a98830baa397.blade.php&line=1", "ajax": false, "filename": "fedc652debeb23dcbb31a98830baa397.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fedc652debeb23dcbb31a98830baa397"}, {"name": "1x __components::ff3b2cf4e42e74e63db76ff05c5f2374", "param_count": null, "params": [], "start": **********.376044, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/ff3b2cf4e42e74e63db76ff05c5f2374.blade.php__components::ff3b2cf4e42e74e63db76ff05c5f2374", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Fff3b2cf4e42e74e63db76ff05c5f2374.blade.php&line=1", "ajax": false, "filename": "ff3b2cf4e42e74e63db76ff05c5f2374.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ff3b2cf4e42e74e63db76ff05c5f2374"}, {"name": "1x __components::745871da7c635a3f461dfaeeef54a48e", "param_count": null, "params": [], "start": **********.379797, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/745871da7c635a3f461dfaeeef54a48e.blade.php__components::745871da7c635a3f461dfaeeef54a48e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F745871da7c635a3f461dfaeeef54a48e.blade.php&line=1", "ajax": false, "filename": "745871da7c635a3f461dfaeeef54a48e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::745871da7c635a3f461dfaeeef54a48e"}, {"name": "1x __components::2b3233eda7e50501ef45fd875b12da49", "param_count": null, "params": [], "start": **********.384307, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/2b3233eda7e50501ef45fd875b12da49.blade.php__components::2b3233eda7e50501ef45fd875b12da49", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F2b3233eda7e50501ef45fd875b12da49.blade.php&line=1", "ajax": false, "filename": "2b3233eda7e50501ef45fd875b12da49.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2b3233eda7e50501ef45fd875b12da49"}, {"name": "1x __components::b13663c834a4ae876ef8f72aa0610e8c", "param_count": null, "params": [], "start": **********.387634, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/b13663c834a4ae876ef8f72aa0610e8c.blade.php__components::b13663c834a4ae876ef8f72aa0610e8c", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Fb13663c834a4ae876ef8f72aa0610e8c.blade.php&line=1", "ajax": false, "filename": "b13663c834a4ae876ef8f72aa0610e8c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b13663c834a4ae876ef8f72aa0610e8c"}, {"name": "1x core/base::layouts.partials.page-header", "param_count": null, "params": [], "start": **********.388482, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/layouts/partials/page-header.blade.phpcore/base::layouts.partials.page-header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fpage-header.blade.php&line=1", "ajax": false, "filename": "page-header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.page-header"}, {"name": "1x core/base::breadcrumb", "param_count": null, "params": [], "start": **********.388977, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/breadcrumb.blade.phpcore/base::breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::breadcrumb"}, {"name": "1x core/base::layouts.partials.footer", "param_count": null, "params": [], "start": **********.390004, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/layouts/partials/footer.blade.phpcore/base::layouts.partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.footer"}, {"name": "1x core/base::partials.copyright", "param_count": null, "params": [], "start": **********.390485, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/partials/copyright.blade.phpcore/base::partials.copyright", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Fcopyright.blade.php&line=1", "ajax": false, "filename": "copyright.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::partials.copyright"}, {"name": "1x core/base::layouts.vertical.partials.after-content", "param_count": null, "params": [], "start": **********.39155, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/layouts/vertical/partials/after-content.blade.phpcore/base::layouts.vertical.partials.after-content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fafter-content.blade.php&line=1", "ajax": false, "filename": "after-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.after-content"}, {"name": "1x core/base::global-search.form", "param_count": null, "params": [], "start": **********.392072, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/global-search/form.blade.phpcore/base::global-search.form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fglobal-search%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::global-search.form"}, {"name": "1x __components::3cec1c87224222bda738c53f782c5bc1", "param_count": null, "params": [], "start": **********.394697, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/3cec1c87224222bda738c53f782c5bc1.blade.php__components::3cec1c87224222bda738c53f782c5bc1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F3cec1c87224222bda738c53f782c5bc1.blade.php&line=1", "ajax": false, "filename": "3cec1c87224222bda738c53f782c5bc1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3cec1c87224222bda738c53f782c5bc1"}, {"name": "1x ********************************::form.index", "param_count": null, "params": [], "start": **********.397822, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/form/index.blade.php********************************::form.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form.index"}, {"name": "1x __components::414e4e803eeec6389552bb46515583c5", "param_count": null, "params": [], "start": **********.399159, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/414e4e803eeec6389552bb46515583c5.blade.php__components::414e4e803eeec6389552bb46515583c5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F414e4e803eeec6389552bb46515583c5.blade.php&line=1", "ajax": false, "filename": "414e4e803eeec6389552bb46515583c5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::414e4e803eeec6389552bb46515583c5"}, {"name": "1x __components::c47a448d99d5719cb034f7947c739ff8", "param_count": null, "params": [], "start": **********.400368, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/c47a448d99d5719cb034f7947c739ff8.blade.php__components::c47a448d99d5719cb034f7947c739ff8", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Fc47a448d99d5719cb034f7947c739ff8.blade.php&line=1", "ajax": false, "filename": "c47a448d99d5719cb034f7947c739ff8.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c47a448d99d5719cb034f7947c739ff8"}, {"name": "1x __components::e226165e1fca7eccb10f6857d7cd235a", "param_count": null, "params": [], "start": **********.401625, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/e226165e1fca7eccb10f6857d7cd235a.blade.php__components::e226165e1fca7eccb10f6857d7cd235a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Fe226165e1fca7eccb10f6857d7cd235a.blade.php&line=1", "ajax": false, "filename": "e226165e1fca7eccb10f6857d7cd235a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e226165e1fca7eccb10f6857d7cd235a"}, {"name": "4x ********************************::modal", "param_count": null, "params": [], "start": **********.402028, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/modal.blade.php********************************::modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal.blade.php&line=1", "ajax": false, "filename": "modal.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::modal"}, {"name": "1x ********************************::custom-template", "param_count": null, "params": [], "start": **********.403073, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/custom-template.blade.php********************************::custom-template", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcustom-template.blade.php&line=1", "ajax": false, "filename": "custom-template.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::custom-template"}, {"name": "1x core/media::partials.media", "param_count": null, "params": [], "start": **********.403702, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/media/resources/views/partials/media.blade.phpcore/media::partials.media", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fmedia%2Fresources%2Fviews%2Fpartials%2Fmedia.blade.php&line=1", "ajax": false, "filename": "media.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/media::partials.media"}, {"name": "4x ********************************::modal.close-button", "param_count": null, "params": [], "start": **********.404626, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/modal/close-button.blade.php********************************::modal.close-button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Fclose-button.blade.php&line=1", "ajax": false, "filename": "close-button.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::modal.close-button"}, {"name": "1x ********************************::loading", "param_count": null, "params": [], "start": **********.405046, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/loading.blade.php********************************::loading", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Floading.blade.php&line=1", "ajax": false, "filename": "loading.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::loading"}, {"name": "1x ********************************::form.checkbox", "param_count": null, "params": [], "start": **********.407422, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form.checkbox"}, {"name": "1x core/media::config", "param_count": null, "params": [], "start": **********.411066, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/media/resources/views/config.blade.phpcore/media::config", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fmedia%2Fresources%2Fviews%2Fconfig.blade.php&line=1", "ajax": false, "filename": "config.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/media::config"}, {"name": "1x ********************************::debug-badge", "param_count": null, "params": [], "start": **********.60999, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/debug-badge.blade.php********************************::debug-badge", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdebug-badge.blade.php&line=1", "ajax": false, "filename": "debug-badge.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::debug-badge"}, {"name": "2x ********************************::modal.action", "param_count": null, "params": [], "start": **********.610821, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/modal/action.blade.php********************************::modal.action", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Faction.blade.php&line=1", "ajax": false, "filename": "action.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::modal.action"}, {"name": "2x ********************************::modal.alert", "param_count": null, "params": [], "start": **********.611685, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/modal/alert.blade.php********************************::modal.alert", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::modal.alert"}, {"name": "1x __components::dcf17957c4aa053a618fd9c312cc29fc", "param_count": null, "params": [], "start": **********.613201, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/dcf17957c4aa053a618fd9c312cc29fc.blade.php__components::dcf17957c4aa053a618fd9c312cc29fc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Fdcf17957c4aa053a618fd9c312cc29fc.blade.php&line=1", "ajax": false, "filename": "dcf17957c4aa053a618fd9c312cc29fc.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dcf17957c4aa053a618fd9c312cc29fc"}, {"name": "1x __components::fcfb9f9ba5fb460899f38b71f491e1fe", "param_count": null, "params": [], "start": **********.61612, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/fcfb9f9ba5fb460899f38b71f491e1fe.blade.php__components::fcfb9f9ba5fb460899f38b71f491e1fe", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Ffcfb9f9ba5fb460899f38b71f491e1fe.blade.php&line=1", "ajax": false, "filename": "fcfb9f9ba5fb460899f38b71f491e1fe.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fcfb9f9ba5fb460899f38b71f491e1fe"}, {"name": "1x ********************************::layouts.base", "param_count": null, "params": [], "start": **********.617194, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/layouts/base.blade.php********************************::layouts.base", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fbase.blade.php&line=1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::layouts.base"}, {"name": "1x core/base::components.layouts.header", "param_count": null, "params": [], "start": **********.617965, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/layouts/header.blade.phpcore/base::components.layouts.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::components.layouts.header"}, {"name": "1x assets::header", "param_count": null, "params": [], "start": **********.621219, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\packages\\laravel-assets\\src\\Providers/../../resources/views/header.blade.phpassets::header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Flaravel-assets%2Fresources%2Fviews%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "assets::header"}, {"name": "1x core/base::elements.common", "param_count": null, "params": [], "start": **********.623245, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/elements/common.blade.phpcore/base::elements.common", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Felements%2Fcommon.blade.php&line=1", "ajax": false, "filename": "common.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::elements.common"}, {"name": "1x assets::footer", "param_count": null, "params": [], "start": **********.625799, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\packages\\laravel-assets\\src\\Providers/../../resources/views/footer.blade.phpassets::footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Flaravel-assets%2Fresources%2Fviews%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "assets::footer"}, {"name": "1x core/base::notification.notification", "param_count": null, "params": [], "start": **********.626904, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/notification/notification.blade.phpcore/base::notification.notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fnotification%2Fnotification.blade.php&line=1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::notification.notification"}]}, "queries": {"count": 5, "nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00201, "accumulated_duration_str": "2.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.970449, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 0, "width_percent": 18.905}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 105}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.976884, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 18.905, "width_percent": 21.393}, {"sql": "select * from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language-advanced/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\language-advanced\\src\\Providers\\HookServiceProvider.php", "line": 154}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.015112, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 40.299, "width_percent": 20.398}, {"sql": "select count(*) as aggregate from `ec_orders` where (`status` = 'pending' and `is_finished` = 1)", "type": "query", "params": [], "bindings": ["pending", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1249}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.1688452, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HookServiceProvider.php:1249", "source": {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1249}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FProviders%2FHookServiceProvider.php&line=1249", "ajax": false, "filename": "HookServiceProvider.php", "line": "1249"}, "connection": "tesmods", "explain": null, "start_percent": 60.697, "width_percent": 22.886}, {"sql": "select count(*) as aggregate from `ec_reviews` where `status` = 'pending'", "type": "query", "params": [], "bindings": ["pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1293}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.268656, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "HookServiceProvider.php:1293", "source": {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1293}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FProviders%2FHookServiceProvider.php&line=1293", "ajax": false, "filename": "HookServiceProvider.php", "line": "1293"}, "connection": "tesmods", "explain": null, "start_percent": 83.582, "width_percent": 16.418}]}, "models": {"data": {"Shaqi\\Language\\Models\\Language": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Shaqi\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://tesmods.gc/admin/ecommerce/options/create", "action_name": "global-option.create", "controller_action": "Shaqi\\Ecommerce\\Http\\Controllers\\ProductOptionController@create", "uri": "GET admin/ecommerce/options/create", "controller": "S<PERSON><PERSON>\\Ecommerce\\Http\\Controllers\\ProductOptionController@create<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FProductOptionController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Shaqi\\Ecommerce\\Http\\Controllers", "prefix": "admin/ecommerce/options", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FProductOptionController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/ecommerce/src/Http/Controllers/ProductOptionController.php:34-39</a>", "middleware": "web, core, auth", "duration": "1.49s", "peak_memory": "66MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1813750079 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1813750079\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2113226022 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2113226022\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1251883158 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">tesmods.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">https://tesmods.gc/admin/ecommerce/options</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2560 characters\">shaqi_footprints_cookie=eyJpdiI6ImxHZGRqajhxQkFDcUMxb2VsdW16d1E9PSIsInZhbHVlIjoiajFBNUdGa2xjRmJPdHdMWFpTRiswdk14REJmbFh3TGp4V0JsUjZpSEhsSENiRWhSRlgwMEl3MTByNEs4UG5qanlaUEc2UlpiOFZVY0tad0g4NnRxc25hZFMvREs4K3U2NEkwcFZBQW9NK3FSdXBpZHpCYnkvUVVRU0xWMEEzMzUiLCJtYWMiOiI2OTk3Mzk3NTRlMzM2ZTdiMDExZWJmODJhZGExOTY4MmZhNmMzNjM3MDM1MWRmMTllMjY5NTUwYjBiODZjZjg1IiwidGFnIjoiIn0%3D; shaqi_footprints_cookie_data=eyJpdiI6ImF4TmlBNFJ3RTVxNFBhWUFLZkNIQWc9PSIsInZhbHVlIjoiVmxhcXR5bU4wOXU5dlhCTjVpK0JWZ2xrT3pZYXkwSllnQ3V6QklLcDRYZVpBeFhmWmJ3WGJwRTA1ZlZmcGkydGdRSHB3ZGJwdUJ3Uk9TYWYvcWtmalNoWi9mWW5SN2hzZTMvanN5cTB5TzBCaGs5NkhWVjljTFRzWWozK0pxN0p4Wjk1ckREWVhleTVjQmJZQ2ttOTlwQ1RDbHhtN3ZOMldQSUhrMExhOHFIc2VCRE9mLy83UmhCSEdkc00rckR1Y3N4NkxML1FNeGZyWitHUldZUkR0eHYyZTdBbEtMRUxqbUtGWithaGJjcVBIV2ZnaElSYllTbE96aTQ5MWNMSkNBa2wwOHM3RFQ0RW83aUpxZmVtYjU4aDhjbmo1clFSWTRHa2VzbEJLOWE3d1NoeVIvQnhrYUM1ejZUUy9qTkY0UDdsb1Q5Z3dybjVuLzZIaXRET1NadjJNSTJsbzNYd2U2aTFMOTMxYTJQejIzTU1IRFFpVm5Nd0xLdUk5Yjh2YXE1S01sRUQrMDB6OXI4U2pZeS9zQTAyaVI5TTN6bWVyV25vWGVtRWlHWkhLb1crRnVXVDJRbjJkNUY0OXM1MUUxMVpiRE9jd0wyQkg0Z0FpcDlzakRlT1NNWnVvSTI4MVNZaC9GZmhuTGNLWXBZRnNTNG9nRnY5Q3RxeTBxWUVRVjNoMDUvTkV1ajRpN096ZW4wOHlBPT0iLCJtYWMiOiJkYmI3NTYwZDUxZWJjY2I0ZmUxMmQ2YmU2YzUxYmQ3MjYzMzJlZmQ1MjMyZTBhNGNlOTUyMTgxMDhjMzhlNjU1IiwidGFnIjoiIn0%3D; cookie_for_consent=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlVHb3RYN0M5L2FRa3ovaExFNWVXQ3c9PSIsInZhbHVlIjoiMFU4V0hvaGRDVXI5K3RzdzdkMjZ1eDZ3cnhXQy9nRUpxekg1bFIxa0VoUTBTY2x6TTB4SlBGL1lkbVpOV2IyRitMcGsyMHFOS2ZMalRDQWZzM1g0MThCcWpheHNMSE5QT2g4ZUhhNFRxTnNUL0ZuKzhyNnpUbXI1V3JMSEhlT0laT2hXN0JUWWd4MEhXMXBhTzhaZkh2VkR4TXF4NXlDNnpUbzcxMjdnL1RwUzduZ05BMy9iTnZDNTlEdlBWaW5RNDkraERQRXI3RmgzNzEzclFrTkQ5S21kRmNWK1I0OVhGUGx2cXpzK0tJdz0iLCJtYWMiOiJhZDg0MDlkNzAxNWI5MjBiN2NlZmE5ZDRkNDExNDg3MjdiMTY1MWY0NDdjNjZkNmI5ZDA1ZTE0MWIwNzc4M2E1IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkUremhUVUVxM2NIZmVGZm50Ni9yOHc9PSIsInZhbHVlIjoiQnY3MDlwRjhCWEJ2WVBSU0x2MnJVY21rWExFTjdkVENTSVR1cU5Mc1oxaE8vb0NseDUxSjhJRFhIbk92SVc2c2lCNkRsRW5kOGdTRDh2MVE2NUt3MHJNdU1xMUlzMDdNZkdrTmQ2OGxxaHVTZ011TWRKMWJPbENwOHV6ZkRSOUIiLCJtYWMiOiI3OWE1MzkyZjQwMzgxMzZmMTg0ZTdlNjU5ZWNkNWE0MTY3NWZmNjFmMDYyYzUwYmE3MDkyMmNjYWUyMmQ3ZmJhIiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6ImkwMU16alAzdHdIZ2htVGU2ZXpnUmc9PSIsInZhbHVlIjoiNkR2UFBKQy9TVUl0SDBURmlBZzFNR0s5M1ZBQVBUUXoyM0kzdjh1Ni80VjdkaVZ1TXdlU3k4SFQwL0s0N0thVEdBUVgrOXppWG9EREpMR0VzRnV1SE9QYWM1UWJJaVNkbEgveEY4TWQrOTRrcWJTQ284WGNkVGtKdlRuWXlXc3giLCJtYWMiOiI1NTRhY2RkNWY2MzA1ZDc5YWY1YjJiYzg5ZjIwZjI0NWI2MTcwZTc2MzZkYmU5NTcwMTFhOTczMjU0MWNhYTM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1251883158\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-574633304 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>shaqi_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7a4e1a8bf2884ebcac0020a875b7168cc919bd88</span>\"\n  \"<span class=sf-dump-key>shaqi_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"346 characters\">{&quot;footprint&quot;:&quot;7a4e1a8bf2884ebcac0020a875b7168cc919bd88&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;tesmods.gc&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|IhQd5rv2OiHN31FogUE4N7nywrkbjngoKJ2fGSpGDJTc8ESMBnaVyTbMe1uM|$2y$12$Y0fKdUtNl.7uriE2jyqMteM5YHmP0ovxpe3RbMWYUS/IwySBzdTYK</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">21kNLtyrrz1EnHCRTflWSzE24GnCtMPJmB7j18C5</span>\"\n  \"<span class=sf-dump-key>shaqi_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">259NiGAyxSp79a8W1RViospfuj3oZ9d3ieXItl0L</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-574633304\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-70215499 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 17:50:14 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-70215499\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-322617068 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">21kNLtyrrz1EnHCRTflWSzE24GnCtMPJmB7j18C5</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">https://tesmods.gc/admin/ecommerce/options</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-322617068\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://tesmods.gc/admin/ecommerce/options/create", "action_name": "global-option.create", "controller_action": "Shaqi\\Ecommerce\\Http\\Controllers\\ProductOptionController@create"}, "badge": null}}